@use '../utils' as *;

.tpshop {
    &__location {
        border: 1px solid var(--tp-border-1);
        border-radius: 6px;
        @media #{$xs} {
            border-radius: 6px;
        }
    }

    &__content {
        padding: 10px 50px;
        @media #{$xs} {
            padding: 20px 12px;
        }
        @media #{$xl,$sm} {
            padding: 5px 20px;
        }
    }

    &__info {
        & ul {
            & li {
                list-style: none;
                position: relative;
                padding-inline-start: 28px;
                margin-bottom: 15px;
                @media #{$xl} {
                    margin-bottom: 7px;
                }
                @media #{$sm} {
                    margin-bottom: 10px;
                }

                &:last-child {
                    margin-bottom: 0;
                }

                & a {
                    font-size: 16px;
                    font-weight: 400;
                    display: block;
                    color: var(--tp-text-secondary);
                    line-height: 26px;
                    @media #{$sm} {
                        font-size: 14px;
                        line-height: 24px;
                    }

                    &:hover {
                        color: var(--tp-text-primary);
                    }

                    & br {
                        @media #{$xxl,$xl,$lg,$md,$xs,$sm} {
                            display: none;
                        }
                    }
                }

                & span {
                    font-size: 16px;
                    font-weight: 400;
                    display: block;
                    color: var(--tp-text-secondary);
                    @media #{$sm} {
                        font-size: 14px;
                        line-height: 24px;
                    }
                }

                & i {
                    position: absolute;
                    top: 8px;
                    inset-inline-start: 0;
                    color: var(--tp-text-body);
                    font-size: 14px;
                }
            }
        }
    }

    &__thumb {
        & img {
            width: 100%;
        }
    }

    &__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--tp-text-body);
        @media #{$xl} {
            font-size: 16px;
        }
        @media #{$sm} {
            font-size: 16px;
            margin-bottom: 7px;
        }
    }

    &__location-map {
        height: 500px;

        & iframe {
            width: 100%;
            height: 100%;
        }
    }

    &__search {
        position: relative;

        & form {
            position: relative;

            & input {
                height: 60px;
                width: 100%;
                border: 1px solid var(--tp-border-1);
                border-radius: 6px;
                padding: 5px 240px 5px 30px;
                @media #{$xs} {
                    font-size: 12px;
                    padding: 5px 150px 5px 15px;
                    height: 48px;
                }

                &::placeholder {
                    font-size: 14px;
                    color: var(--tp-grey-9);
                }
            }
        }

        & button {
            position: absolute;
            inset-inline-end: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            font-weight: 600;
            color: var(--tp-common-white);
            background-color: var(--tp-text-primary);
            border-radius: 0px 6px 6px 0px;
            padding: 17px 32px;
            @media #{$xs} {
                font-size: 12px;
                padding: 10px 10px;
            }

            & i {
                margin-inline-start: 10px;
            }
        }
    }
}

.tpcontact {
    &__right {
        margin-inline-end: 75px;
        @media #{$xl} {
            margin-inline-end: 20px;
        }
        @media #{$lg,$md,$xs,$sm} {
            margin-inline-end: 0px;
        }
    }

    &__shop {
        border: 1px solid var(--tp-border-1);
        border-radius: 6px;
        padding: 45px 35px 45px 35px;
        @media #{$lg} {
            padding: 45px 15px 45px 15px;
        }

        & .tpshop__title {
            font-size: 26px;
            font-weight: 600;
        }
    }

    &__title {
        font-size: 26px;
        font-weight: 600;
        color: var(--tp-text-body);
        margin-bottom: 0;
    }

    &__info {
        & p {
            font-size: 14px;
            color: var(--tp-text-secondary);
        }
    }

    &__input {
        & input,
        & textarea {
            height: 60px;
            width: 100%;
            padding: 5px 20px;
            border: 1px solid var(--tp-border-1);
            border-radius: 6px;

            &:focus {
                border: 1px solid var(--tp-text-primary);
                outline: 0;
            }

            &::placeholder {
                font-size: 16px;
                color: var(--tp-text-secondary);
            }
        }

        & textarea {
            height: 240px;
            padding: 25px;
        }
    }

    &__support {
        & a {
            display: block;
            font-weight: 600;
            font-size: 16px;
            border: 1px solid var(--tp-border-1);
            border-radius: 6px;
            padding: 16px 5px;
            text-align: center;
            @include transition(0.3s);
            margin-bottom: 10px;

            &:hover {
                background-color: var(--tp-text-primary);
                color: var(--tp-common-white);
                border: 1px solid var(--tp-text-primary);

                & i {
                    color: var(--tp-common-white);
                }
            }

            & i {
                color: var(--tp-text-primary);
                margin-inline-start: 14px;
            }
        }
    }
}

.tperror {
    &__title {
        font-size: 48px;
        font-weight: 600;
        color: var(--tp-text-body);
    }

    &__content {
        & p {
            font-size: 18px;
            font-weight: 400;
            color: var(--tp-text-secondary);
            margin-bottom: 40px;
        }
    }
}

.tp-error-btn {
    & i {
        margin-inline-end: 15px;
    }
}

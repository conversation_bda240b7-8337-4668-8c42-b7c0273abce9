<?php

namespace Bo<PERSON>ble\Ecommerce\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\DatePickerFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\HtmlFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Botble\Base\Forms\Fields\DatePickerField;
use Botble\Base\Forms\Fields\EmailField;
use Botble\Base\Forms\Fields\HtmlField;
use Botble\Base\Forms\Fields\MediaImagesField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\Ecommerce\Forms\Fronts\Auth\FieldOptions\EmailFieldOption;
use Botble\Ecommerce\Forms\Fronts\Auth\FieldOptions\TextFieldOption;
use <PERSON><PERSON><PERSON>\Ecommerce\Http\Requests\ReviewRequest;
use Bo<PERSON>ble\Ecommerce\Models\Review;
use Carbon\Carbon;

class ReviewForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(Review::class)
            ->setValidatorClass(ReviewRequest::class)
            ->add(
                'product_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/ecommerce::review.product'))
                    ->ajaxSearch()
                    ->required()
                    ->ajaxUrl(route('reviews.ajax-search-products'))
            )
            ->add(
                'customer_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/ecommerce::review.choose_existing_customer'))
                    ->ajaxSearch()
                    ->ajaxUrl(route('reviews.ajax-search-customers'))
                    ->helperText(trans('plugins/ecommerce::review.choose_customer_help'))
            )
            ->add(
                'open_or',
                HtmlField::class,
                HtmlFieldOption::make()
                    ->content(sprintf(
                        '<div class="form-fieldset"><label class="form-label">%s</label>',
                        trans('plugins/ecommerce::review.or_enter_manually')
                    ))
            )
            ->add(
                'customer_name',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/ecommerce::ecommerce.customer_name'))
            )
            ->add(
                'customer_email',
                EmailField::class,
                EmailFieldOption::make()
                    ->label(trans('plugins/ecommerce::ecommerce.customer_email'))
            )
            ->add(
                'close_or',
                HtmlField::class,
                HtmlFieldOption::make()
                    ->content('</div>')
            )
            ->add(
                'star',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/ecommerce::review.star'))
                    ->choices(array_combine(range(1, 5), range(1, 5)))
                    ->selected(5)
            )
            ->add(
                'comment',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/ecommerce::review.comment'))
                    ->required()
            )
            ->add('images[]', MediaImagesField::class, [
                'label' => trans('plugins/ecommerce::review.images'),
                'values' => $this->model->images,
            ])
            ->add(
                'created_at',
                DatePickerField::class,
                DatePickerFieldOption::make()
                    ->label(trans('core/base::tables.created_at'))
                    ->value(Carbon::now())
                    ->withTimePicker()
            )
            ->setBreakFieldPoint('created_at');
    }
}

<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Http\Requests;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Rules\EmailRule;
use Bo<PERSON>ble\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Ecommerce\Models\Customer;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class AddCustomerWhenCreateOrderRequest extends Request
{
    public function rules(): array
    {
        if (! EcommerceHelper::isUsingInMultipleCountries()) {
            $this->merge(['country' => EcommerceHelper::getFirstCountryId()]);
        }

        $rules = EcommerceHelper::getCustomerAddressValidationRules();

        $rules['phone'] = 'required|' . BaseHelper::getPhoneValidationRule();

        $rules['email'] = ['nullable', new EmailRule(), Rule::unique((new Customer())->getTable(), 'email')];

        return $rules;
    }
}

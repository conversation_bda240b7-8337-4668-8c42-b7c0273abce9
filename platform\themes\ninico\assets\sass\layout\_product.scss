@use '../utils' as *;

.tpproduct {
    &:hover {
        & .tpproduct__priceinfo-list {
            bottom: 0px;
            visibility: hidden;
            opacity: 0;
        }

        & .tpproduct__cart {
            visibility: visible;
            opacity: 1;
            bottom: -20px;
        }

        & .product-thumb-secondary {
            opacity: 1;

            & img {
                transform: scale3d(1.1, 1.1, 1.1) translateZ(0);
            }
        }
    }

    &__thumb {
        position: relative;
        margin-bottom: 17px;

        &:hover {
            & .tpproduct__thumb-action {
                & a {
                    -webkit-transform: translate(0, 0);
                    -ms-transform: translate(0, 0);
                    -o-transform: translate(0, 0);
                    transform: translate(0, 0);
                    visibility: visible;
                    opacity: 1;
                    filter: alpha(opacity=100);
                }
            }
        }

        &-discount {
            position: absolute;
            top: 25px;
            inset-inline-start: 25px;
            padding: 4px 7px;
            background-color: var(--tp-text-primary);
            font-weight: 600;
            font-size: 12px;
            color: var(--tp-common-white);
            line-height: 1;
            border-radius: 5px;
            animation: firefly 2s linear infinite;
            z-index: 2;
        }

        &-topsall {
            position: absolute;
            top: 25px;
            inset-inline-start: 25px;
            padding: 4px 7px;
            background-color: var(--tp-text-primary);
            font-weight: 600;
            font-size: 12px;
            color: var(--tp-common-white);
            line-height: 1;
            border-radius: 5px;
            animation: firefly 2s linear infinite;
            z-index: 2;
        }

        &-volt {
            position: absolute;
            top: 25px;
            inset-inline-start: 25px;
            padding: 4px 7px;
            background-color: var(--tp-theme-5);
            font-weight: 600;
            font-size: 12px;
            color: var(--tp-common-white);
            line-height: 1;
            border-radius: 5px;
            animation: firefly 2s linear infinite;
            z-index: 2;
        }

        &-action {
            position: absolute;
            inset-inline-end: 0;
            inset-inline-start: 0;
            margin: auto;
            text-align: center;
            top: 50%;
            transform: translateY(-50%);

            & a {
                font-size: 16px;
                color: var(--tp-text-body);
                height: 50px;
                width: 50px;
                text-align: center;
                line-height: 50px;
                background-color: var(--tp-common-white);
                border-radius: 6px;
                display: inline-block;
                box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
                opacity: 0;
                visibility: hidden;
                -webkit-transform: translate(0, 20px);
                -ms-transform: translate(0, 20px);
                -o-transform: translate(0, 20px);
                transform: translate(0, 20px);
                -webkit-box-shadow: 0px 4px 11px 0px rgb(0, 0, 0, 0.3);
                -moz-box-shadow: 0px 4px 11px 0px rgb(0, 0, 0, 0.3);

                &:hover {
                    background-color: var(--tp-text-primary);
                    color: var(--tp-common-white);
                }

                &.compare {
                    -webkit-transition:
                        transform 0.3s ease 0s,
                        background-color 0.3s ease 0s,
                        opacity 0.3s ease 0s;
                    -o-transition:
                        transform 0.3s ease 0s,
                        background-color 0.3s ease 0s,
                        opacity 0.3s ease 0s;
                    transition:
                        transform 0.3s ease 0s,
                        background-color 0.3s ease 0s,
                        opacity 0.3s ease 0s;
                }

                &.quickview {
                    -webkit-transition:
                        transform 0.4s ease 0s,
                        background-color 0.4s ease 0s,
                        opacity 0.4s ease 0s;
                    -o-transition:
                        transform 0.4s ease 0s,
                        background-color 0.4s ease 0s,
                        opacity 0.4s ease 0s;
                    transition:
                        transform 0.4s ease 0s,
                        background-color 0.4s ease 0s,
                        opacity 0.4s ease 0s;
                }

                &.wishlist {
                    -webkit-transition:
                        transform 0.6s ease 0s,
                        background-color 0.6s ease 0s,
                        opacity 0.6s ease 0s;
                    -o-transition:
                        transform 0.6s ease 0s,
                        background-color 0.6s ease 0s,
                        opacity 0.6s ease 0s;
                    transition:
                        transform 0.6s ease 0s,
                        background-color 0.6s ease 0s,
                        opacity 0.6s ease 0s;
                }
            }
        }

        &-bg {
            position: absolute;
            inset-inline-start: 0;
            inset-inline-end: 0;
            margin: auto;
            text-align: center;
            top: 50%;
            transform: translateY(-50%);

            & a {
                margin: 0 10px;
                font-size: 16px;
                padding: 14px 0;
                display: inline-block;
                line-height: 1;
                color: #999999;

                &:hover {
                    color: var(--tp-text-body);
                }
            }
        }

        & img {
            border-radius: 6px;
            width: 100%;
        }

        &-icon {
            position: relative;
            z-index: 2;
        }
    }

    &__rating {
        display: flex;
        align-items: center;
        gap: 5px;

        & a {
            font-size: 10px;
            color: var(--tp-text-primary);
        }

        & span {
            font-size: 10px;
        }
    }

    &__ratingicon {
        & a {
            font-size: 14px;
            color: var(--tp-text-primary);
        }

        & .max-star {
            color: #e0e0e0;
        }
    }

    &__ratingarea {
        position: absolute;
        inset-inline-start: 0;
        inset-inline-end: 0;
        transition: all 0.2s cubic-bezier(0.28, 0.12, 0.22, 1);
        will-change: opacity, transform;
        z-index: 2;
        opacity: 0;
        visibility: hidden;
        padding-bottom: 18px;
        transform: translateY(8px);
    }

    &__content-area {
        z-index: 2;
        position: relative;
        transform: translateY(5px);
        transition: 0.2s;
        margin-bottom: 10px;
    }

    &__thumbitem {
        position: relative;
        z-index: 2;

        &:hover {
            & .thumbitem-secondary {
                opacity: 1;

                & img {
                    transform: scale3d(1.1, 1.1, 1.1) translateZ(0);
                }
            }

            & .tpproductactionbg {
                transform: translate(0, 0);
                opacity: 1;
                visibility: visible;
            }
        }
    }

    &__title {
        font-size: 15px;
        font-weight: 400;
        color: var(--tp-text-secondary);
        margin-bottom: 12px;

        & a {
            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__priceinfo {
        &-list {
            position: absolute;
            bottom: -20px;
            inset-inline-start: 0;
            @include transition(0.3s);

            & span {
                font-weight: 600;
                font-size: 16px;
                color: var(--tp-text-primary);
            }

            &-oldprice {
                color: var(--tp-text-body) !important;
                text-decoration: line-through;
                margin-inline-start: 8px;
            }
        }

        &-oldprice {
            text-decoration: line-through;
        }
    }

    &__amount {
        font-size: 16px;
        font-weight: 600;
        color: var(--tp-text-primary);

        &.old {
            color: var(--tp-text-body);
            text-decoration: line-through;
        }
    }

    &__cart {
        position: absolute;
        inset-inline-start: 0;
        bottom: -40px;
        inset-inline-start: 0;
        visibility: hidden;
        opacity: 0;
        @include transition(0.3s);

        & i {
            color: var(--tp-text-primary);
            margin-inline-end: 5px;
        }

        & a {
            font-size: 16px;
            font-weight: 600;
            text-decoration: underline;
            @include transition(0.3s);

            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__variation {
        position: absolute;
        z-index: 2;
        background: #fff;
        bottom: -8px;
        inset-inline-start: 0;
        inset-inline-end: 0;
        padding-top: 12px;

        & a {
            &:hover {
                & .tpproduct__termshape-bg {
                    transform: scale(0.7);
                }

                & .tpproduct__termshape-border {
                    opacity: 1;
                    transform: scale(1.4);
                }
            }
        }
    }

    &__variationitem {
        display: inline-block;
    }

    &__termshape {
        position: relative;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-inline-end: 3px;

        &-bg {
            transition: all 0.25s ease;
            display: block;
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: #99c4ff;
        }

        &-border {
            display: block;
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 1px solid #99c4ff;
            opacity: 0;
            @include transition(0.3s);
        }
    }
}

.tpproductactionbg {
    background-color: var(--tp-common-white);
    display: inline-block;
    padding: 0 8px;
    border-radius: 6px;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
    transform: translate(0, 20px);
    opacity: 0;
    visibility: hidden;
    transition:
        transform 0.5s ease 0s,
        background-color 0.5s ease 0s,
        opacity 0.4s ease 0s;
}

.tpproductdot {
    & a {
        &:hover {
            & .tpproductdot__termshape-bg {
                transform: scale(0.7);
            }

            & .tpproductdot__termshape-border {
                opacity: 1;
                transform: scale(1.8);
            }
        }
    }

    &__variationitem {
        inset-inline-start: 0;
        inset-inline-end: 0;
        padding-top: 12px;
    }

    &__variationitem {
        display: inline-block;
    }

    &__termshape {
        position: relative;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-inline-end: 3px;

        &-bg {
            transition: all 0.25s ease;
            display: block;
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: #99c4ff;
        }

        &-border {
            display: block;
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 1px solid #99c4ff;
            opacity: 0;
            @include transition(0.3s);
        }
    }
}

.red-product-bg {
    background-color: var(--tp-text-primary);
}

.red-product-border {
    border: 1px solid var(--tp-text-primary);
}

.purple-product-bg {
    background-color: #be9aff;
}

.purple-product-border {
    border: 1px solid #be9aff;
}

.orange-product-bg {
    background-color: #ff7373;
}

.orange-product-border {
    border: 1px solid #ff7373;
}

.tpproductitem {
    z-index: 9;
    background-color: #fff;
    padding-bottom: 10px;

    &:hover {
        & .product-content-fade {
            opacity: 1;
        }

        & .tpproduct__ratingarea {
            opacity: 1;
            visibility: visible;
            transform: translateY(0px);
        }

        &:after {
            opacity: 1;
        }
    }
}

.tpproductitem:after {
    position: absolute;
    top: -20px;
    bottom: -10px;
    inset-inline-start: -20px;
    inset-inline-end: -20px;
    background-color: #fff;
    border-radius: 5px;
    opacity: 0;
    -webkit-transition: opacity 0.2s cubic-bezier(0.28, 0.12, 0.22, 1);
    transition: opacity 0.2s cubic-bezier(0.28, 0.12, 0.22, 1);
    will-change: opacity;
    box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.06);
    pointer-events: none;
    z-index: 1;
    margin-bottom: -33px;
    content: '';
}

.red-product-bg {
    background-color: var(--tp-text-primary);
}

.red-product-border {
    border: 1px solid var(--tp-text-primary);
}

.yellow-product-bg {
    background-color: #ffd8a6;
}

.yellow-product-border {
    border: 1px solid #ffd8a6;
}

.green-product-bg {
    background-color: #c2ddce;
}

.green-product-border {
    border: 1px solid #c2ddce;
}

// deal-product
.tpdealproduct {
    &__thumb {
        & img {
            @media #{$xl,$lg,$md,$xs} {
                max-width: 100%;
            }
        }
    }

    &__offer {
        &-price {
            position: absolute;
            top: 75px;
            inset-inline-end: 155px;
            font-size: 24px;
            font-weight: 600;
            color: var(--tp-common-white);
            background-color: var(--tp-text-primary);
            border-radius: 50%;
            padding: 19px 18px;
            animation: hotdeal 2s linear infinite;

            & span {
                font-size: 12px;
                display: block;
                color: var(--tp-common-white);
            }
        }
    }
}

.tpdealcontact {
    margin-inline-start: -50px;
    padding-inline-end: 179px;
    @media #{$xxl,$xl} {
        padding-inline-end: 50px;
    }
    @media #{$xl} {
        margin-inline-start: 0px;
        padding-inline-end: 0px;
    }
    @media #{$lg} {
        margin-inline-start: 0;
        padding-inline-end: 20px;
    }
    @media #{$md} {
        margin-inline-start: 20px;
        padding-inline-end: 20px;
    }
    @media #{$xs} {
        margin-inline-start: 15px;
        padding-inline-end: 15px;
    }

    &__price {
        & span {
            font-size: 20px;
            font-weight: 600;
            color: var(--tp-text-primary);
            @media #{$xs} {
                font-size: 16px;
            }
        }

        & del {
            font-size: 20px;
            font-weight: 600;
            @media #{$xs} {
                font-size: 16px;
            }
        }
    }

    &__title {
        font-size: 50px;
        color: var(--tp-text-body);
        font-weight: 600;
        line-height: 1.2;
        letter-spacing: -1.5px;
        @media #{$xl} {
            font-size: 42px;
        }
        @media #{$xs} {
            font-size: 23px;
            letter-spacing: 0;
        }
        @media #{$sm} {
            font-size: 24px;
            letter-spacing: 0;
        }
        @media #{$lg} {
            font-size: 32px;
        }
    }

    &__text {
        & p {
            color: var(--tp-text-secondary);
            font-size: 18px;
            line-height: 1.66;
            @media #{$xl} {
                width: 520px;
            }
            @media #{$lg} {
                font-size: 16px;
                width: 410px;
                line-height: 1.5;
            }
            @media #{$xs} {
                font-size: 16px;
                width: 100%;
                line-height: 1.4;
            }
        }
    }

    &__progress {
        & .progress {
            height: 6px;
            border-radius: 5px;
            background-color: var(--tp-common-white);
        }

        & .progress-bar {
            color: var(--tp-common-white);
            text-align: center;
            white-space: nowrap;
            background-color: var(--tp-text-primary);
            transition: width 0.6s ease;
            border-radius: 5px;
        }
    }

    &__countdown {
        & span {
            display: inline-block;

            &.cdown {
                background-color: var(--tp-common-white);
                height: 92px;
                width: 80px;
                text-align: center;
                line-height: 65px;
                color: var(--tp-text-body);
                font-size: 36px;
                font-weight: 700;
                border-radius: 6px;
                margin-inline-end: 15px;
                box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
                @media #{$xs} {
                    margin-bottom: 10px;
                }

                & p {
                    margin-top: -15px;
                    font-size: 16px;
                    color: var(--tp-text-secondary);
                }
            }
        }
    }

    &__count {
        display: flex;
        align-items: center;
        @media #{$lg,$xs} {
            display: inherit;
        }

        & i {
            font-style: normal;
            font-size: 16px;
            margin-bottom: 0;
            padding-inline-start: 13px;
            color: var(--tp-text-secondary);
            @media #{$lg,$xs} {
                padding-top: 10px;
                padding-inline-start: 0px;
                display: block;
            }

            & br {
                @media #{$lg,$xs} {
                    display: none;
                }
            }
        }
    }
}

.swiper-container.product-active {
    margin-inline-end: -500px;
}

.discount-product {
    z-index: 1;

    & .tpdealcontact {
        margin-inline-start: -13px;
        padding-inline-end: 140px;
        @media #{$xxl} {
            padding-inline-end: 40px;
        }
        @media #{$xl} {
            margin-inline-start: 0;
            padding-inline-end: 10px;
        }
        @media #{$lg} {
            margin-inline-start: 0;
            padding-inline-end: 10px;
        }
        @media #{$md,$xs} {
            margin-inline-start: 15px;
            padding-inline-end: 15px;
        }
    }
}

.tpdealproduct {
    margin-inline-start: 68px;
    @media #{$lg} {
        margin-inline-start: 10px;
    }
    @media #{$md,$xs} {
        margin-inline-start: 0px;
        margin-bottom: 30px;
    }
}

.tp-fourth-deal {
    & .tpdealcontact {
        margin-inline-start: 0px;
        padding-inline-end: 0px;
        @media #{$xl,$lg} {
            padding-inline-end: 10px;
        }
    }
}

.video-icon {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    inset-inline-start: 50%;
}

.platinamdell {
    .tpdealcontact {
        margin-inline-start: 0;
    }
}

.tpdealslide {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;

    &::before {
        position: absolute;
        content: '';
        top: 0;
        inset-inline-start: 0;
        width: 100%;
        height: 100%;
        background-color: var(--tp-theme-13);
    }
}

.tp-red-deal-text {
    padding-inline-end: 0;
}

.tpvideo-bg {
    position: absolute;
    inset-inline-end: 0;
    top: 0;
    height: 100%;
    width: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    @media #{$md} {
        position: relative;
        height: 450px;
        width: 100%;
    }
    @media #{$xs} {
        position: relative;
        height: 450px;
        width: 100%;
    }

    & a {
        height: 120px;
        width: 120px;
        line-height: 120px;
        text-align: center;
        background-color: var(--tp-common-white);
        border-radius: 50%;
        color: var(--tp-theme-primary);
        display: block;
        font-size: 20px;
        animation: tp-pulse-2 1s infinite;
        @media #{$xs} {
            height: 80px;
            width: 80px;
            line-height: 80px;
        }

        &:hover {
            background-color: var(--tp-text-primary);
            color: var(--tp-common-white);
        }
    }
}

.video-area {
    @media #{$xs} {
        padding-top: 0 !important;
    }
}

.product-thumb-secondary,
.thumbitem-secondary {
    overflow: hidden;
    position: absolute;
    inset-inline-start: 0;
    top: 0;
    opacity: 0;
    border-radius: 6px;

    & img {
        transition:
            opacity 0.8s ease,
            transform 1.1s cubic-bezier(0.15, 0.75, 0.5, 1) 0s,
            -webkit-transform 1.1s cubic-bezier(0.15, 0.75, 0.5, 1) 0s;
    }
}

.product-item-count {
    & span {
        color: #999;
        font-size: 18px;

        & b {
            font-weight: 600;
            color: var(--tp-common-black);
        }
    }
}

.product-filter-nav {
    margin-inline-start: 5px;
    @media #{$xs} {
        transform: translateY(15px);
    }

    &.tpnavbar .nav-link {
        margin-inline-start: 15px;
    }
}

.tp-shop-selector {
    & .nice-select {
        border: transparent;
        color: var(--tp-text-body);
        font-weight: 600;

        &::after {
            inset-inline-end: 20px;
        }

        @media #{$xs} {
            padding-inline-start: 0;
        }
    }
}

.product-navtabs {
    @media #{$xs} {
        justify-content: start !important;
    }
    @media #{$sm} {
        justify-content: end !important;
    }
}

.filter-product {
    @media #{$md,$xs} {
        margin-inline-start: 0;
    }

    & p {
        font-size: 16px;
    }
}

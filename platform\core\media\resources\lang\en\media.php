<?php

return [
    'filter' => 'Filter',
    'everything' => 'Everything',
    'image' => 'Image',
    'video' => 'Video',
    'document' => 'Document',
    'view_in' => 'View in',
    'all_media' => 'All media',
    'trash' => 'Trash',
    'recent' => 'Recent',
    'favorites' => 'Favorites',
    'upload' => 'Upload',
    'create_folder' => 'Create folder',
    'refresh' => 'Refresh',
    'empty_trash' => 'Empty trash',
    'search_file_and_folder' => 'Search in current folder',
    'sort' => 'Sort',
    'file_name_asc' => 'File name - ASC',
    'file_name_desc' => 'File name - DESC',
    'uploaded_date_asc' => 'Uploaded date - ASC',
    'uploaded_date_desc' => 'Uploaded date - DESC',
    'size_asc' => 'Size - ASC',
    'size_desc' => 'Size - DESC',
    'actions' => 'Actions',
    'nothing_is_selected' => 'Nothing is selected',
    'insert' => 'Insert',
    'folder_name' => 'Folder name',
    'create' => 'Create',
    'rename' => 'Rename',
    'crop' => 'Crop',
    'close' => 'Close',
    'save_changes' => 'Save changes',
    'move_to_trash' => 'Move items to trash',
    'confirm_trash' => 'Are you sure you want to move these items to trash?',
    'confirm' => 'Confirm',
    'confirm_delete' => 'Delete item(s)',
    'confirm_delete_description' => 'This action is irreversible. Are you sure want to delete these items?',
    'empty_trash_title' => 'Empty trash',
    'empty_trash_description' => 'This action is irreversible. Are you sure you want to permanently delete all items in trash?',
    'up_level' => 'Up one level',
    'upload_progress' => 'Upload progress',
    'alt_text' => 'Alt text',

    'folder_created' => 'Folder created successfully!',
    'gallery' => 'Media gallery',

    'trash_error' => 'Error when deleting selected item(s)',
    'trash_success' => 'Moved selected item(s) to trash successfully!',
    'move_error' => 'Error when moving selected item(s)',
    'move_success' => 'Moved selected item(s) successfully!',
    'selected_items_move_success' => 'Moved selected item(s) to :name successfully!',
    'selected_items_move_error' => 'Error when moving selected item(s) to :name',
    'destination' => 'Destination',
    'restore_error' => 'Error when restoring selected item(s)',
    'restore_success' => 'Restore selected item(s) successfully!',
    'copy_success' => 'Copied selected item(s) successfully!',
    'delete_success' => 'Deleted selected item(s) successfully!',
    'favorite_success' => 'Favorite selected item(s) successfully!',
    'remove_favorite_success' => 'Remove selected item(s) from favorites successfully!',
    'rename_error' => 'Error when rename item(s)',
    'rename_success' => 'Rename selected item(s) successfully!',
    'crop_success' => 'Crop image successfully!',
    'empty_trash_success' => 'Empty trash successfully!',
    'invalid_action' => 'Invalid action!',
    'file_not_exists' => 'File is not exists!',
    'download_file_error' => 'Error when downloading files!',
    'missing_zip_archive_extension' => 'Please enable ZipArchive extension to download file!',
    'can_not_download_file' => 'Can not download this file!',
    'invalid_request' => 'Invalid request!',
    'add_success' => 'Add item successfully!',
    'file_too_big' => 'File is too big. Max file upload is :size bytes',
    'file_too_big_readable_size' => 'File is too big. Max file upload is :size.',
    'can_not_detect_file_type' => 'File type is not allowed or can not detect file type!',
    'upload_failed' => 'The file is NOT uploaded completely. The server allows max upload file size is :size . Please check your file size OR try to upload again in case of having network errors',
    'failed_to_crop_image' => 'The file cropping must be image type',
    'menu_name' => 'Media',
    'description' => 'Manage and view Media Files',
    'panel' => [
        'title' => 'Manage Media Files',
        'description' => 'Manage and view Media Files',
    ],
    'add' => 'Add media',

    'javascript' => [
        'name' => 'Name',
        'url' => 'URL',
        'full_url' => 'Full URL',
        'alt' => 'Alt text',
        'size' => 'Size',
        'mime_type' => 'Type',
        'created_at' => 'Uploaded at',
        'updated_at' => 'Modified at',
        'nothing_selected' => 'Nothing is selected',
        'visit_link' => 'Open link',
        'width' => 'Width',
        'height' => 'Height',

        'no_item' => [
            'all_media' => [
                'title' => 'Drop files and folders here',
                'message' => 'Or use the upload button above',
            ],
            'trash' => [
                'title' => 'There is nothing in your trash currently',
                'message' => 'Delete files to move them to trash automatically. Delete files from trash to remove them permanently',
            ],
            'favorites' => [
                'title' => 'You have not added anything to your favorites yet',
                'message' => 'Add files to favorites to easily find them later',
            ],
            'recent' => [
                'title' => 'You did not opened anything yet',
                'message' => 'All recent files that you opened will be appeared here',
            ],
            'default' => [
                'title' => 'No items',
                'message' => 'This directory has no item',
            ],
        ],

        'clipboard' => [
            'success' => 'These file links have been copied to clipboard',
        ],

        'message' => [
            'error_header' => 'Error',
            'success_header' => 'Success',
        ],

        'download' => [
            'error' => 'No files selected or cannot download these files',
        ],

        'actions_list' => [
            'basic' => [
                'preview' => 'Preview',
                'crop' => 'Crop',
            ],
            'file' => [
                'copy_link' => 'Copy link',
                'copy_indirect_link' => 'Copy indirect link',
                'rename' => 'Rename',
                'make_copy' => 'Make a copy',
                'alt_text' => 'ALT text',
                'share' => 'Share',
            ],
            'user' => [
                'favorite' => 'Add to favorite',
                'remove_favorite' => 'Remove favorite',
            ],
            'other' => [
                'download' => 'Download',
                'trash' => 'Move to trash',
                'delete' => 'Delete permanently',
                'restore' => 'Restore',
                'properties' => 'Properties',
            ],
        ],
        'change_image' => 'Change image',
        'delete_image' => 'Delete image',
        'choose_image' => 'Choose image',
        'preview_image' => 'Preview image',
    ],
    'name_invalid' => 'The folder name has invalid character(s).',
    'add_from_url' => 'Add from URL',
    'or' => 'or',
    'url_invalid' => 'Please provide a valid URL',
    'path_invalid' => 'Please provide a valid path',
    'download_link' => 'Download',
    'url' => 'URL',
    'download_explain' => 'Enter one URL per line.',
    'downloading' => 'Downloading...',
    'prepare_file_to_download' => 'Preparing file to download...',
    'update_alt_text_success' => 'Update alt text successfully!',
    'upload_from_local' => 'Upload from local',
    'upload_from_url' => 'Upload from URL',

    'cropper' => [
        'height' => 'Height',
        'width' => 'Width',
        'aspect_ratio' => 'Aspect ratio',
    ],
    'unable_to_write' => 'Unable to write file. Please chmod folder ":folder" to make it writeable!',
    'unable_download_image_from' => 'Unable to download image from URL: :url',
    'rename_physical_folder' => 'Rename physical folder name on disk too',
    'rename_physical_file' => 'Rename physical file name on disk too',
    'rename_physical_file_warning' => 'This option will rename physical file name on disk too. It may cause broken links if you are using this file in other places.',
    'properties' => [
        'name' => 'Properties',
        'color_label' => 'Choose a color for this folder',
    ],
    'update_properties_success' => 'Update properties successfully!',
    'share' => 'Share',
    'share_type' => 'Share Type',
    'share_as_url' => 'URL',
    'share_as_indirect_url' => 'Indirect URL',
    'share_as_html' => 'HTML',
    'share_as_markdown' => 'Markdown',
    'share_results' => 'Share Results',
    'download_image_to_local_storage' => 'Download image to local storage',
    'download_image_to_local_storage_helper' => 'If it is unchecked, the image will be displayed from the original URL',
    'skip_trash' => 'Skip trash',
    'skip_trash_description' => 'If it is checked, the file will be deleted permanently without moving to trash',
];

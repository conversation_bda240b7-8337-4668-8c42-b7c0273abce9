<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\DataSynchronize\Http\Controllers\ImportController;
use Bo<PERSON>ble\DataSynchronize\Importer\Importer;
use Botble\Ecommerce\Importers\ProductImporter;
use Illuminate\Http\Request;

class ImportProductController extends ImportController
{
    protected function getImporter(): Importer
    {
        return ProductImporter::make();
    }

    protected function prepareImporter(Request $request): Importer
    {
        /**
         * @var ProductImporter $importer
         */
        $importer= parent::prepareImporter($request);

        return $importer->setImportType($request->input('type'));
    }
}

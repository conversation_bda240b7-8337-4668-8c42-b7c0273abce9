<?php

namespace Bo<PERSON>ble\Location\Http\Requests;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Rules\MediaImageRule;
use Bo<PERSON>ble\Base\Rules\OnOffRule;
use Bo<PERSON>ble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CityRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:250'],
            'state_id' => ['nullable', 'exists:states,id'],
            'country_id' => ['required', 'exists:countries,id'],
            'slug' => [
                'nullable',
                'string',
                Rule::unique('cities', 'slug')->ignore($this->route('city')),
            ],
            'image' => ['nullable', 'string', new MediaImageRule()],
            'order' => ['required', 'integer', 'min:0', 'max:127'],
            'status' => [Rule::in(BaseStatusEnum::values())],
            'is_default' => [new OnOffRule()],
            'zip_code' => ['nullable', ...BaseHelper::getZipcodeValidationRule(true)],
        ];
    }
}

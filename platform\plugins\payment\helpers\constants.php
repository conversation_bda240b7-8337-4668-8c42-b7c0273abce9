<?php

if (! defined('PAYMENT_MODULE_SCREEN_NAME')) {
    define('PAYMENT_MODULE_SCREEN_NAME', 'payment');
}

if (! defined('ACTION_AFTER_UPDATE_PAYMENT')) {
    define('ACTION_AFTER_UPDATE_PAYMENT', 'action_after_update_payment');
}

if (! defined('PAYMENT_METHODS_SETTINGS_PAGE')) {
    define('PAYMENT_METHODS_SETTINGS_PAGE', 'payment-methods');
}

if (! defined('PAYMENT_METHOD_SETTINGS_CONTENT')) {
    define('PAYMENT_METHOD_SETTINGS_CONTENT', 'payment-method-setting-content');
}

if (! defined('PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS')) {
    define('PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS', 'payment-additional-methods');
}

if (! defined('PAYMENT_FILTER_PAYMENT_PARAMETERS')) {
    define('PAYMENT_FILTER_PAYMENT_PARAMETERS', 'payment-parameters');
}

if (! defined('PAYMENT_FILTER_AFTER_POST_CHECKOUT')) {
    define('PAYMENT_FILTER_AFTER_POST_CHECKOUT', 'payment-after-post-checkout');
}

if (! defined('PAYMENT_FILTER_PAYMENT_INFO_DETAIL')) {
    define('PAYMENT_FILTER_PAYMENT_INFO_DETAIL', 'payment-info-detail');
}

if (! defined('PAYMENT_ACTION_PAYMENT_PROCESSED')) {
    define('PAYMENT_ACTION_PAYMENT_PROCESSED', 'payment-action-payment-processed');
}

if (! defined('PAYMENT_FILTER_REDIRECT_URL')) {
    define('PAYMENT_FILTER_REDIRECT_URL', 'payment-filter-redirect-url');
}

if (! defined('PAYMENT_FILTER_CANCEL_URL')) {
    define('PAYMENT_FILTER_CANCEL_URL', 'payment-filter-cancel-url');
}

if (! defined('PAYMENT_FILTER_GET_SERVICE_CLASS')) {
    define('PAYMENT_FILTER_GET_SERVICE_CLASS', 'payment-filter-get-service-class');
}

if (! defined('PAYMENT_FILTER_GET_REFUND_DETAIL')) {
    define('PAYMENT_FILTER_GET_REFUND_DETAIL', 'payment-refund-detail');
}

if (! defined('PAYMENT_FILTER_PAYMENT_FORM')) {
    define('PAYMENT_FILTER_PAYMENT_FORM', 'payment-form');
}

if (! defined('PAYMENT_FILTER_HEADER_ASSETS')) {
    define('PAYMENT_FILTER_HEADER_ASSETS', 'payment-header-assets');
}

if (! defined('PAYMENT_FILTER_FOOTER_ASSETS')) {
    define('PAYMENT_FILTER_FOOTER_ASSETS', 'payment-footer-assets');
}

if (! defined('PAYMENT_FILTER_PAYMENT_DATA')) {
    define('PAYMENT_FILTER_PAYMENT_DATA', 'payment-data');
}

if (! defined('PAYMENT_FILTER_AFTER_PAYMENT_METHOD')) {
    define('PAYMENT_FILTER_AFTER_PAYMENT_METHOD', 'payment-after-payment-method');
}

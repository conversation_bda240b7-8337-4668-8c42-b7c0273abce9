<?php

namespace Bo<PERSON>ble\Faq\Http\Controllers\Settings;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Faq\Forms\Settings\FaqSettingForm;
use Bo<PERSON>ble\Faq\Http\Requests\Settings\FaqSettingRequest;
use Bo<PERSON><PERSON>\Setting\Http\Controllers\SettingController;

class FaqSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/faq::faq.settings.title'));

        return FaqSettingForm::create()->renderForm();
    }

    public function update(FaqSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate($request->validated());
    }
}

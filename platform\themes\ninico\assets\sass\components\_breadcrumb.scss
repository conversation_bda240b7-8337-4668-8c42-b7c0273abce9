@use '../utils' as *;

.tp-breadcrumb {
    &__bg {
        background-position: right;
        background-repeat: repeat;
        background-size: cover;
        @media #{$xs} {
            background-position: left;
        }
    }

    &__title {
        font-size: 44px;
        font-weight: 600;
        color: var(--tp-text-body);
        @media #{$xs} {
            font-size: 36px;
        }
    }

    &__link {
        & span {
            font-size: 16px;
            display: inline-block;
            color: var(--tp-text-secondary);
            font-weight: 400;

            & a {
                &:hover {
                    color: var(--tp-text-primary);
                }
            }
        }

        & .breadcrumb-item-active {
            position: relative;
            color: var(--tp-text-body);
            padding-inline-end: 40px;

            &::before {
                position: absolute;
                content: '';
                height: 2px;
                width: 30px;
                background-color: #e4ded5;
                top: 50%;
                transform: translate(-50%);
                inset-inline-end: -13px;
            }
        }
    }
}

.basic-pagination {
    & ul {
        & li {
            display: inline-block;
            list-style: none;

            &:not(:last-child) {
                margin-inline-end: 5px;
            }

            & a,
            & span {
                display: inline-block;
                width: 50px;
                height: 50px;
                line-height: 48px;
                text-align: center;
                @include border-radius(6px);
                border: 1px solid var(--tp-theme-7);
                font-size: 14px;
                font-weight: 600;
                color: var(--tp-text-body);
                @include transition(0.2s);
                @media #{$xs} {
                    height: 30px;
                    width: 30px;
                    line-height: 28px;
                    font-size: 12px;
                }
                @media #{$sm} {
                    height: 40px;
                    width: 40px;
                    line-height: 36px;
                    font-size: 14px;
                }

                &:hover,
                &.current {
                    background: var(--tp-text-primary);
                    border-color: var(--tp-text-primary);
                    color: var(--tp-common-white);
                }

                & i {
                    font-weight: 600;
                }
            }
        }
    }
}

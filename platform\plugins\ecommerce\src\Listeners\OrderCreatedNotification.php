<?php

namespace Bo<PERSON>ble\Ecommerce\Listeners;

use Bo<PERSON>ble\Base\Events\AdminNotificationEvent;
use Bo<PERSON>ble\Base\Supports\AdminNotificationItem;
use Bo<PERSON>ble\Ecommerce\Events\OrderCreated;
use Bo<PERSON>ble\Ecommerce\Events\OrderPlacedEvent;

class OrderCreatedNotification
{
    public function handle(OrderPlacedEvent|OrderCreated $event): void
    {
        event(new AdminNotificationEvent(
            AdminNotificationItem::make()
                ->title(trans('plugins/ecommerce::order.new_order_notifications.new_order'))
                ->description(trans('plugins/ecommerce::order.new_order_notifications.description', [
                    'customer' => $event->order->shippingAddress->name,
                    'quantity' => $quantity = $event->order->products->count(),
                    'product' => $quantity > 1 ? trans('plugins/ecommerce::order.new_order_notifications.products') : trans('plugins/ecommerce::order.new_order_notifications.product'),
                ]))
                ->action(trans('plugins/ecommerce::order.new_order_notifications.view'), route('orders.edit', $event->order->getKey()))
        ));
    }
}

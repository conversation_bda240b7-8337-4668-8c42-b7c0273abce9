<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Http\Requests;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use <PERSON><PERSON>ble\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Models\Product;
use Botble\Payment\Enums\PaymentMethodEnum;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class CreateOrderRequest extends Request
{
    public function rules(): array
    {
        $rules = [
            'customer_id' => ['required', 'exists:ec_customers,id'],
            'products' => ['required', 'array'],
            'products.*.id' => ['required', 'exists:ec_products,id'],
        ];

        $products = Product::query()
            ->whereIn('id', collect($this->input('products'))->pluck('id')->all())
            ->get();

        if (EcommerceHelper::isAvailableShipping($products)) {
            $rules['customer_address.phone'] = 'required|' . BaseHelper::getPhoneValidationRule();
            $rules = [...$rules, ...EcommerceHelper::getCustomerAddressValidationRules('customer_address.')];
        }

        if (is_plugin_active('payment')) {
            $rules['payment_status'] = Rule::in(PaymentStatusEnum::values());
            $rules['transaction_id'] = [Rule::when(
                in_array(
                    $this->input('payment_method'),
                    [PaymentMethodEnum::COD, PaymentMethodEnum::BANK_TRANSFER]
                ),
                'nullable',
                'required'
            )];
        }

        return $rules;
    }

    public function attributes(): array
    {
        return [
            'customer_id' => trans('plugins/ecommerce::order.customer_label'),
            'customer_address.phone' => trans('plugins/ecommerce::order.phone'),
        ];
    }

    public function messages(): array
    {
        return [
            'products.required' => trans('plugins/ecommerce::order.requires_products_to_create_order'),
        ];
    }
}

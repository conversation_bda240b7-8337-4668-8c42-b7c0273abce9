$((function(){var e="ecommerce-tax-rule-table",r="#"+e,t=$(".create-tax-rule-form-modal"),a=t.find(".modal-body"),o=t.find(".modal-title strong"),n=function(e){a.html(e.data.html),o.text(e.message||"...")};t.on("show.bs.modal",(function(){a.html('<div class=\'w-100 text-center py-3\'><div class="spinner-border" role="status">\n        <span class="visually-hidden">Loading...</span>\n    </div></div>'),o.text("...")})),$(document).off("click",".create-tax-rule-item").on("click",".create-tax-rule-item",(function(e){e.preventDefault();var r=$(e.currentTarget);t.modal("show"),$.ajax({url:r.find("[data-action=create]").data("href"),success:function(e){0==e.error?(n(e),Botble.initResources()):Botble.showError(e.message)},error:function(e){Botble.handleError(e)}})})),$(document).on("click",r+" .btn-edit-item",(function(e){e.preventDefault();var r=$(e.currentTarget);t.modal("show"),$.ajax({url:r.prop("href"),success:function(e){0==e.error?(n(e),Botble.initResources()):Botble.showError(e.message)},error:function(e){Botble.handleError(e)}})})),$(document).on("submit","#ecommerce-tax-rule-form",(function(r){r.preventDefault();var a=$(r.currentTarget);$.ajax({url:a.prop("action"),method:"POST",data:a.serializeArray(),success:function(r){0==r.error?(window.LaravelDataTables&&window.LaravelDataTables[e]&&LaravelDataTables[e].draw(),t.modal("hide")):Botble.showError(r.message)},error:function(e){Botble.handleError(e)}})}))}));
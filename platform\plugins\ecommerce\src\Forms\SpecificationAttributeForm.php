<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Base\Forms\FormAbstract;
use Botble\Base\Forms\MetaBox;
use Botble\Ecommerce\Enums\SpecificationAttributeFieldType;
use Bo<PERSON>ble\Ecommerce\Forms\Fronts\Auth\FieldOptions\TextFieldOption;
use Botble\Ecommerce\Http\Requests\SpecificationAttributeRequest;
use Botble\Ecommerce\Models\SpecificationAttribute;
use Bo<PERSON>ble\Ecommerce\Models\SpecificationGroup;

class SpecificationAttributeForm extends FormAbstract
{
    public function setup(): void
    {
        $options = $this->getModel()->options ?? [];
        $options = is_array($options) ? $options : json_decode($options, true);

        $this
            ->model(SpecificationAttribute::class)
            ->setValidatorClass(SpecificationAttributeRequest::class)
            ->add(
                'group_id',
                SelectField::class,
                SelectFieldOption::make()
                    ->required()
                    ->label(trans('plugins/ecommerce::product-specification.specification_attributes.group'))
                    ->attributes([
                        'placeholder' => trans('plugins/ecommerce::product-specification.specification_attributes.group_placeholder'),
                    ])
                    ->choices(SpecificationGroup::query()->pluck('name', 'id')->all())
            )
            ->add(
                'name',
                TextField::class,
                NameFieldOption::make()
                    ->required(),
            )
            ->add(
                'type',
                SelectField::class,
                SelectFieldOption::make()
                    ->required()
                    ->label(trans('plugins/ecommerce::product-specification.specification_attributes.type'))
                    ->choices(SpecificationAttributeFieldType::labels())
            )
            ->add(
                'default_value',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/ecommerce::product-specification.specification_attributes.default_value'))
            )
            ->addMetaBox(
                MetaBox::make('specification-attribute-options')
                    ->hasTable()
                    ->attributes([
                        'class' => 'specification-attribute-options',
                        'style' => sprintf(
                            'display: %s;',
                            in_array(old('type', $this->getModel()), [SpecificationAttributeFieldType::SELECT, SpecificationAttributeFieldType::RADIO]) ? 'block' : 'none;'
                        ),
                    ])
                    ->title(trans('plugins/ecommerce::product-specification.specification_attributes.options.heading'))
                    ->content(view('plugins/ecommerce::specification-attributes.partials.options', compact('options')))
                    ->footerContent(view('plugins/ecommerce::specification-attributes.partials.options-footer'))
            );
    }
}

@use '../utils' as *;

.header-welcome-text {
    @media #{$lg,$md} {
        text-align: center;
    }

    & span {
        @media #{$xs} {
            font-size: 14px;
        }
    }

    & a {
        color: var(--tp-text-primary);
        font-weight: 600;
        display: inline-block;
        position: relative;
        @media #{$xs} {
            font-size: 14px;
        }

        &::before {
            position: absolute;
            content: '';
            height: 1px;
            width: 100%;
            background-color: var(--tp-text-primary);
            bottom: 0;
        }

        & i {
            margin-inline-start: 8px;
        }

        &:hover {
            & i {
                animation: iconarrow 0.4s linear;
            }
        }
    }
}

.header-search-bar {
    width: 100%;

    & .header-search-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        inset-inline-end: 20px;
    }

    & input {
        border: none;
        height: 50px;
        width: 100%;
        padding: 5px 45px 5px 25px;
        background-color: var(--tp-grey-2);
        border-radius: 6px;

        &.has-category-select {
            padding: 5px 45px 5px 175px;
        }

        &::placeholder {
            color: var(--tp-grey-1);
        }
    }
}

.header-meta {
    margin-inline-start: 40px;
    @media #{$xl} {
        margin-inline-start: 0;
    }
    @media #{$xs} {
        transform: translateY(4px);
    }

    &__lang {
        & > ul {
            & > li {
                position: relative;
                list-style: none;

                & > a, & > button {
                    border: 1px solid var(--tp-border-1);
                    display: flex;
                    align-items: center;
                    padding: 6px 19px 6px 6px;
                    border-radius: 5px;
                    margin-inline-end: 20px;
                    font-weight: 700;
                    font-size: 14px;

                    & img {
                        margin-inline-end: 12px;
                    }

                    & i {
                        color: var(--tp-grey-3);
                        font-size: 15px;
                        margin-inline-start: 8px;
                    }
                }

                &:hover {
                    & .header-meta__lang-submenu {
                        opacity: 1;
                        visibility: visible;
                        top: 100%;
                    }

                    & a {
                        & i {
                            transform: rotate(180deg);
                            color: var(--tp-text-primary);
                        }
                    }
                }
            }
        }
    }

    &__lang-submenu {
        position: absolute;
        top: 110%;
        inset-inline-start: 0;
        width: 140px;
        background: var(--tp-common-white);
        z-index: 9;
        box-shadow: 0 30px 70px 6px rgba(11, 6, 70, 0.08);
        padding: 15px 20px;
        border-radius: 4px;
        opacity: 0;
        visibility: hidden;
        @include transition(0.3s);

        & li {
            list-style: none;
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 0;
            }

            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__value {
        & span {
            font-weight: 700;
            font-size: 14px;
        }

        & .nice-select {
            height: 50px;
            line-height: 47px;
            width: 95px;

            &:focus {
                border-color: var(--tp-text-primary);
            }
        }
    }

    &__social {
        & a {
            margin-inline-start: 25px;
            font-size: 20px;
            @media #{$lg} {
                margin-inline-start: 15px;
            }
            @media #{$xs} {
                margin-inline-start: 15px;
                font-size: 17px;
            }
            @media #{$sm} {
                margin-inline-start: 25px;
            }

            &::hover {
                color: var(--tp-common-black);
            }
        }
    }

    &__search-5 {
        position: relative;

        & input {
            height: 50px;
            width: 200px;
            background-color: transparent;
            border: 1px solid var(--tp-border-1);
            border-radius: 6px;
            padding: 2px 10px 2px 45px;

            &:focus {
                border: 1px solid var(--tp-text-primary);
            }

            &::placeholder {
                font-size: 14px;
                color: var(--tp-text-5);
            }
        }

        & .header-search-icon-5 {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            inset-inline-start: 18px;
            color: var(--tp-text-body);
            font-size: 14px;
        }
    }
}

.header-cart {
    & span {
        display: inline-block;
        height: 15px;
        width: 15px;
        line-height: 15px;
        border-radius: 50%;
        background-color: var(--tp-text-primary);
        color: var(--tp-common-white);
        font-size: 12px;
        text-align: center;
        position: absolute;
        top: -4px;
        inset-inline-end: -8px;
    }
}

.cat-menu {
    &__category {
        & > .tp-cat-toggle {
            display: flex;
            align-items: center;
            padding: 9px 25px;
            min-height: 52px;
            min-width: 215px;
            font-size: 16px;
            color: var(--tp-common-white);
            font-weight: 600;
            border-radius: 6px 6px 0 0;
            background: var(--tp-text-primary);
            @media #{$xxl} {
                min-width: 205px;
            }
            @media #{$xl,$lg} {
                min-width: 100%;
            }

            & i {
                margin-inline-end: 15px;
            }
        }

        & .category-menu {
            background: var(--tp-common-white);
            width: 100%;
            border: 1px solid var(--tp-border-2);
            border-top: none;
            position: absolute;
            top: 100%;
            inset-inline-start: 0;
            border-radius: 0 0 6px 6px;
            z-index: 9;

            & .daily-offer {
                padding: 14px 0;
                background-color: var(--tp-border-2);
                border-radius: 0 0 6px 6px;
                @media #{$xxl,$xl} {
                    padding: 14px 0;
                }

                & ul {
                    & li {
                        list-style: none;
                        padding: 3px 20px 3px 25px;
                        @media #{$xxl} {
                            padding: 0px 20px 0px 25px;
                        }
                        @media #{$xl} {
                            padding: 3px 20px 3px 15px;
                        }

                        & a {
                            background-image: linear-gradient(var(--tp-text-primary), var(--tp-text-primary)),
                                linear-gradient(var(--tp-text-primary), var(--tp-text-primary));
                            display: inline;
                            background-size:
                                0% 1px,
                                0 1px;
                            background-position:
                                100% 100%,
                                0 90%;
                            background-repeat: no-repeat;
                            transition: background-size 0.4s linear;
                            @include transition(0.3s);
                            font-size: 14px;
                            font-weight: 600;

                            &:hover {
                                color: var(--tp-text-primary);
                                background-size:
                                    0 1px,
                                    100% 1px;
                            }
                        }
                    }
                }
            }
        }
    }

    &__list {
        padding: 10px 0;

        & li {
            list-style: none;

            & > a {
                display: flex;
                color: var(--tp-text-body);
                font-size: 14px;
                font-weight: 400;
                padding: 10px 25px 5px 25px;
                background: transparent;
                align-items: center;
                @include transition(0.3s);
                position: relative;
                @media #{$xl} {
                    padding: 9px 15px 5px 15px;
                }

                &:hover {
                    color: var(--tp-text-primary);

                    &::after {
                        color: var(--tp-text-primary);
                    }
                }

                & i {
                    width: 32px;
                    color: var(--tp-text-primary);
                    @media #{$xl} {
                        width: 28px;
                    }
                }
            }

            &.menu-item-has-children {
                &:hover {
                    & .submenu {
                        opacity: 1;
                        visibility: visible;
                        pointer-events: auto;
                    }
                }

                & > a {
                    &::after {
                        content: '\f178';
                        position: absolute;
                        inset-inline-end: 20px;
                        font-size: 14px;
                        font-family: 'Font Awesome 5 Pro';
                        font-weight: 700;
                        color: var(--tp-grey-3);
                    }

                    &:hover {
                        &::after {
                            color: var(--tp-text-primary);
                        }
                    }
                }

                & .submenu {
                    position: absolute;
                    width: 100%;
                    inset-inline-start: 100%;
                    inset-inline-end: 0;
                    top: 0;
                    padding-inline-start: 0;
                    padding-top: 1.25rem;
                    list-style: none;
                    background-color: var(--tp-common-white);
                    border-inline-start: 1px solid var(--tp-border-1);
                    border-top: 0;
                    white-space: nowrap;
                    z-index: 100;
                    opacity: 0;
                    visibility: hidden;
                    pointer-events: none;
                    -webkit-transition: all 0.25s cubic-bezier(0.28, 0.12, 0.22, 1);
                    transition: all 0.25s cubic-bezier(0.28, 0.12, 0.22, 1);
                }
            }
        }
    }
}

.main-menu {
    & ul {
        & > li {
            position: relative;
            list-style: none;
            display: inline-block;
            margin-inline-end: 40px;
            @media #{$xxl} {
                margin-inline-end: 50px;
            }
            @media #{$xl} {
                margin-inline-end: 22px;
            }
            @media #{$lg} {
                margin-inline-end: 40px;
            }

            & > a {
                position: relative;
                font-weight: 600;
                font-size: 14px;
                color: var(--tp-text-body);
                padding: 15px 0 23px 0;
                display: block;
                line-height: 1;
            }

            &.has-dropdown {
                & > a {
                    position: relative;

                    &::after {
                        transform: translateY(2px);
                        content: '\f107';
                        font-size: 14px;
                        color: var(--tp-grey-3);
                        font-family: var(--tp-ff-fontawesome);
                        margin-inline-start: 4px;
                        display: inline-block;
                        font-weight: 400;
                    }
                }
            }

            & .submenu {
                position: absolute;
                top: 120%;
                inset-inline-start: 0;
                min-width: 250px;
                padding: 15px 0;
                background-color: var(--tp-common-white);
                z-index: 2;
                transition:
                    opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1),
                    visibility 0.4s cubic-bezier(0.19, 1, 0.22, 1),
                    transform 0.4s cubic-bezier(0.19, 1, 0.22, 1);
                box-shadow: 0px 8px 20px rgba(61, 110, 168, 0.1);
                border-radius: 0px 0px 10px 10px;
                border-top: 2px solid var(--tp-text-primary);
                opacity: 0;
                visibility: hidden;
                transition: 0.1s;

                & li {
                    display: block;
                    width: 100%;
                    margin: 0;

                    &.has-dropdown {
                        & > a {
                            &::after {
                                position: absolute;
                                top: 50%;
                                inset-inline-end: 25px;
                                @include transform(translateY(-50%) rotate(-90deg));
                            }
                        }
                    }

                    & a {
                        padding: 12px 25px;
                        font-size: 15px;
                        z-index: 1;
                        color: var(--tp-common-black);
                        width: 100%;
                        font-weight: 500;

                        &::before {
                            display: none;
                        }
                    }

                    & .submenu {
                        inset-inline-start: 120%;
                        top: 0;
                        visibility: hidden;
                        opacity: 0;
                    }

                    &:hover {
                        & > a {
                            color: var(--tp-text-primary);
                            margin-inline-start: 8px;
                        }

                        & .mega-menu-title {
                            margin-inline-start: 0;
                        }

                        & > .submenu {
                            inset-inline-start: 100%;
                            visibility: visible;
                            opacity: 1;
                        }
                    }
                }
            }

            & .mega-menu {
                padding: 20px 40px 20px 40px;
                top: 120%;
                width: 800px;
                background-color: var(--tp-common-white);
                inset-inline-start: 0;
                inset-inline-end: 0;
                z-index: 5;
                transform-origin: top;
                flex: 0 0 auto;
                display: flex;
                margin: 0 auto;
                box-shadow: 0px 8px 20px rgba(61, 110, 168, 0.1);
                border-radius: 0px 0px 10px 10px;
                border-top: 2px solid var(--tp-text-primary);
                @include transition(0.1s);

                & li {
                    width: 260px;
                    text-align: left;
                    padding-inline-start: 0;
                    padding-inline-end: 0;

                    & .mega-menu-title {
                        display: inline-flex;
                        align-items: center;
                        font-weight: 600;
                        font-size: 13px;
                        position: relative;
                        text-transform: uppercase;
                        color: var(--tp-common-black);
                        margin-bottom: 10px;
                        padding-inline-start: 0;
                    }

                    & ul {
                        padding-inline-start: 0;
                        text-align: left;

                        & li {
                            padding-inline-start: 0;
                            text-align: left;

                            & a {
                                position: relative;
                                padding: 10px;
                                font-size: 16px;
                                font-weight: 400;
                                color: var(--tp-text-body);
                                text-transform: capitalize;
                                text-align: start;
                                margin-inline-end: 8px;
                                padding-inline-start: 0;

                                &:hover {
                                    color: var(--tp-text-primary);
                                }
                            }
                        }
                    }
                }
            }

            &:hover {
                & > a {
                    color: var(--tp-text-primary);

                    &::after {
                        color: var(--tp-text-primary);
                    }

                    &::before {
                        width: 41px;
                    }
                }

                & > .submenu {
                    top: 100%;
                    visibility: visible;
                    opacity: 1;
                    z-index: 99;
                }
            }
        }
    }
}

.menu-contact {
    display: flex;
    justify-content: flex-end;

    & ul {
        & li {
            display: inline-block;
            margin-inline-end: 40px;
            @media #{$xl} {
                margin-inline-end: 22px;
            }
            @media #{$lg} {
                margin-inline-end: 12px;
            }

            &:last-child {
                margin-inline-end: 0;
            }
        }
    }

    &__item {
        display: flex;
        align-items: center;
    }

    &__icon {
        color: var(--tp-text-primary);
        font-size: 15px;
        margin-inline-end: 8px;
        @media #{$lg} {
            margin-inline-end: 4px;
        }
    }

    &__info {
        & a {
            font-weight: 600;
            font-size: 14px;
            @media #{$lg} {
                font-size: 12px;
            }

            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }
}

.mainmenu__search-icon {
    i {
        color: var(--tp-text-body);
    }
}

.headertoplag {
    @media #{$xs} {
        justify-content: center !important;
    }

    &__lang {
        & > ul {
            & > li {
                position: relative;
                list-style: none;

                & > a, & > button {
                    display: inline-block;
                    padding: 6px 19px 8px 6px;
                    margin-inline-end: 10px;
                    font-weight: 700;
                    font-size: 14px;
                    color: var(--tp-text-body);

                    &:hover {
                        color: var(--tp-text-primary);
                    }

                    & img {
                        margin-inline-end: 5px;
                    }

                    & i {
                        color: var(--tp-grey-3);
                        font-size: 14px;
                    }
                }

                &:hover {
                    & .header-meta__lang-submenu {
                        opacity: 1;
                        visibility: visible;
                        top: 100%;
                    }

                    & a {
                        color: var(--tp-text-body);

                        &:hover {
                            color: var(--tp-text-primary);
                        }

                        & i {
                            transform: rotate(180deg);
                            color: var(--tp-text-primary);
                        }
                    }
                }
            }
        }
    }
}

.menu-top-social {
    & a {
        font-size: 14px;
        margin-inline-start: 10px;
        color: var(--tp-text-body);

        &:hover {
            color: var(--tp-text-primary);
        }
    }
}

.mainmenu {
    &__search {
        &-bar {
            & input {
                border: none;
                height: 50px;
                width: 200px;
                padding: 5px 20px 5px 45px;
                background-color: var(--tp-grey-2);
                border-radius: 6px;
                color: var(--tp-text-body);

                &::placeholder {
                    font-size: 14px;
                    color: #9999;
                }
            }
        }

        &-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            inset-inline-start: 20px;
        }
    }

    &__main {
        margin-inline-start: 120px;
        @media #{$xl} {
            margin-inline-start: 50px;
        }

        & > .main-menu {
            & a {
                padding: 45px 0;
            }
        }
    }

    &__logo {
        position: absolute;
        top: 48%;
        inset-inline-start: 47%;
        transform: translateY(-52%);
    }
}

.tertiary-header-top {
    & .headertoplag__lang {
        & ul {
            & li {
                &:hover {
                    & i {
                        transform: rotate(0);
                    }
                }

                & a, & button {
                    color: var(--tp-text-body);

                    &:hover {
                        & i {
                            color: var(--tp-text-primary);
                        }

                        &.order-tick {
                            & i {
                                animation: iconarrow 0.4s linear;
                                color: var(--tp-text-primary);
                            }
                        }
                    }

                    & i {
                        color: var(--tp-text-body);
                        margin-inline-end: 6px;
                    }
                }
            }
        }
    }
}

.tertiary-main-menu {
    border-top: 1px solid #efefef;

    & .main-menu {
        & ul {
            & li {
                & a {
                    padding: 21px 0 20px 0;
                }
            }
        }
    }

    & .cat-menu__category {
        & > .tp-cat-toggle {
            border-radius: 0;
            min-height: 55px;
        }
    }

    & .category-menu {
        background-color: var(--tp-common-white);
        z-index: 99;
    }
}

.coupon-offer {
    padding: 15px 25px 15px 25px;
    background-color: var(--tp-text-body);

    & span {
        color: #868686;
        font-size: 14px;

        & a {
            color: var(--tp-common-white);
            text-decoration: underline;
            font-weight: 600;

            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    & i {
        text-align: end;
        color: #535353;

        &:hover {
            color: var(--tp-text-primary);
        }
    }
}

.platinam-header-top {
    & .header-welcome-text {
        & span {
            font-size: 14px;
            font-weight: 400;
        }

        & b {
            font-size: 14px;
            font-weight: 600;
        }
    }
}

.platinam-menuarea {
    & .mainmenu__search-bar {
        & input {
            background-color: transparent;
        }
    }

    & .mainmenu__main {
        margin-inline-start: 0;
    }
}

.header-canvas {
    flex: 0 0 auto;
    margin-inline-end: 15px;
    line-height: 1;
    margin-top: 10px;
    font-size: 22px;
}

.menu-area-4 {
    display: flex;
    justify-content: center;
}

.red-header-top {
    & .menu-top-social {
        & a {
            font-size: 16px;
            color: var(--tp-common-white);
            margin-inline-start: 45px;

            &:first-child {
                margin-inline-start: 0;
            }

            &:hover {
                opacity: 0.8;
            }
        }
    }

    & .header-welcome-text {
        font-size: 16px;
        color: var(--tp-common-white);
        font-weight: 400;
        @media #{$md} {
            text-align: center !important;
        }
        @media #{$xs} {
            text-align: start !important;
        }

        & a {
            color: var(--tp-common-white);
            text-decoration: underline;
        }
    }
}

.category-style-five {
    & .category-menu {
        position: static;
    }
}

.mainmenu-5 {
    & .main-menu {
        & a {
            padding: 45px 0;
        }
    }
}

.tpsideinfo {
    background: var(--tp-text-body);
    position: fixed;
    inset-inline-start: 0;
    top: 0;
    height: 100%;
    padding: 30px;
    width: 350px;
    transform: translateX(-120%);
    transition: 0.3s;
    z-index: 999;
    overflow-y: auto;
    @media #{$xs} {
        width: 275px;
    }

    &.tp-sidebar-opened {
        transform: translateX(0);
    }

    &__close {
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        inset-inline-end: 0;
        color: var(--tp-common-white);
        width: 100%;
        display: block;
        min-height: 45px;
        text-transform: uppercase;
        font-size: 13px;
        font-weight: 600;
        background-color: var(--tp-text-primary);
    }

    &__search-title {
        color: var(--tp-common-white);
        font-size: 13px;
        font-weight: 500;
        text-transform: uppercase;
    }

    &__search {
        & form {
            position: relative;
            padding-top: 13px;
            padding-bottom: 20px;

            & input {
                width: 100%;
                height: 45px;
                border-radius: 3px;
                font-size: 14px;
                border: 1px solid transparent;
                background: #f3f3f9;
                padding: 10px 20px;
                padding-inline-end: 45px;
            }
        }

        & button {
            position: absolute;
            inset-inline-end: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    &__nabtab {
        & .nav-link.active {
            border: 0;
            padding: 10px 15px;
            position: relative;
            text-transform: uppercase;
            font-size: 13px;
            font-weight: 500;
            border-radius: 3px;
            background-color: var(--tp-text-primary);
        }

        & .nav-link {
            border: 0;
            padding: 10px 15px;
            position: relative;
            text-transform: uppercase;
            font-size: 13px;
            font-weight: 500;
            border-radius: 3px;
            color: var(--tp-common-black);
            background-color: var(--tp-common-white);
        }

        & button {
            width: 100%;
        }

        & .nav {
            & li {
                display: inline-block;
                width: 49%;
                margin-inline-end: 4px;

                &:last-child {
                    margin-inline-end: 0;
                }
            }
        }

        & .mega-menu-title {
            color: var(--tp-common-white);
            font-size: 13px;
            text-transform: uppercase;
            padding-top: 5px;
        }

        & .mean-container .mean-nav ul {
            background-image: none !important;
        }

        & .home-menu-style {
            & li {
                width: 50% !important;

                & a {
                    font-size: 13px !important;
                    font-weight: 400 !important;
                }
            }
        }
    }

    &__account-link,
    &__wishlist-link {
        padding-top: 5px;
        padding-bottom: 5px;

        & a {
            font-size: 16px;
            color: var(--tp-common-white);
            font-weight: 500;

            &:hover {
                color: var(--tp-text-primary);

                & i {
                    color: var(--tp-text-primary);
                }
            }

            & i {
                font-size: 18px;
                color: var(--tp-common-white);
                line-height: 35px;
                text-align: center;
                margin-inline-end: 10px;
            }
        }
    }
}

.body-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    height: 100%;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 100;
    inset-inline-end: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-out 0s;

    &.opened {
        opacity: 1;
        visibility: visible;
    }
}

.tp-cart-info-area {
    &.tp-sidebar-opened {
        transform: translateX(0);
    }
}

.tp-sidebar-close {
    color: var(--tp-common-white);
    position: absolute;
    inset-inline-start: -35px;
    font-size: 21px;
    background: #171151;
    width: 35px;
    height: 35px;
}

.tpcartinfo {
    background-color: var(--tp-common-white);
    text-align: start;
    position: fixed;
    inset-inline-end: 0;
    top: 0;
    height: 100%;
    box-shadow: rgba(5, 13, 54, 0.05) 5px 15px 30px 0;
    transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    z-index: 9999999;
    width: 380px;
    transform: translateX(100%);
    @media #{$xs} {
        width: 280px;
    }

    & .tp-shop-sidebar-opened {
        transform: translateX(0);
    }
}

.tpcart {
    &__close {
        color: var(--tp-text-primary);
        inset-inline-end: 30px;
        font-size: 18px;
        width: 35px;
        height: 35px;
        position: absolute;
        top: 8px;
        z-index: 2;
        @media #{$xs} {
            width: 30px;
            height: 30px;
        }
    }
}

.cartbody-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    height: 100%;
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 100;
    inset-inline-start: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-out 0s;

    &.opened {
        opacity: 1;
        visibility: visible;
    }
}

.tpcart {
    float: none;
    height: 100%;
    overflow: hidden;
    position: relative;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex: 1 1 auto;
    align-items: stretch;
    flex-direction: column;

    &__product {
        position: relative;
        display: flex;
        flex: 1 1 auto;
        align-items: stretch;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
        padding: 0 30px;
    }

    &__title {
        padding: 16px 30px;
        background-color: #f8f8f8;
        font-weight: 700;
        font-size: 15px;
        color: var(--tp-text-body);
        text-transform: uppercase;
        margin-bottom: 20px;
    }

    & ul {
        & li {
            list-style: none;
            padding-top: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--tp-border-1);
        }
    }

    &__item {
        display: flex;
        align-items: center;
        flex: 0 0 auto;
    }

    &__img {
        margin-inline-end: 20px;
        position: relative;
        @media #{$xs} {
            margin-inline-end: 4px;
        }

        & img {
            width: 100px;
            height: 100px;
            border-radius: 10px;
            object-fit: cover;
        }
    }

    &__del {
        position: absolute;
        color: var(--tp-text-primary);
        inset-inline-start: 0;
        top: 0;
    }

    &__content-title {
        font-size: 14px;
        font-weight: 400;
        color: var(--tp-text-body);

        & a {
            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__cart-price {
        font-weight: 600;
        font-size: 12px;
        color: var(--tp-text-body);

        & .new-price {
            color: var(--tp-text-primary);
        }
    }

    &__total-price {
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        color: var(--tp-text-primary);
        margin-bottom: 25px;
        padding-top: 25px;
        border-top: 1px solid var(--tp-border-1);

        & .heilight-price {
            font-size: 18px;
            font-weight: 700;
        }
    }

    &__free-shipping {
        padding: 13px 30px;
        background-color: var(--tp-theme-5);

        & span {
            color: var(--tp-common-white);
            font-size: 14px;
            @media #{$xs} {
                font-size: 12px;
            }

            & b {
                font-weight: 600;
                text-transform: uppercase;
            }
        }
    }

    &__checkout {
        margin-bottom: 30px;
    }
}

.header-sticky {
    position: fixed;
    inset-inline-start: 0;
    margin: auto;
    top: 0;
    width: 100%;
    box-shadow: 0 0 60px 0 rgba(0, 0, 0, 0.07);
    z-index: 999;
    animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
    background: var(--tp-common-white);
}

.header-sticky .tp-bt-btn {
    box-shadow: rgba(100, 100, 111, 0.1) 1px 5px 20px 1px;
}

.tp-home-one.header-sticky {
    padding-top: 0;
    padding-bottom: 0;
}

.tp-home-one.header-sticky .main-menu nav > ul > li > a {
    padding: 40px 0;
}

.tp-mobile-header-area.header-sticky {
    position: fixed;
    inset-inline-start: 0;
    margin: auto;
    top: 0;
    width: 100%;
    box-shadow: 0 0 60px 0 rgba(0, 0, 0, 0.07);
    z-index: 99;
    animation: 300ms ease-in-out 0s normal none 1 running fadeInDown;
    background: var(--tp-common-white);
}

.tp-mobile-header-area.header-sticky .tp-bt-btn {
    box-shadow: rgba(100, 100, 111, 0.1) 1px 5px 20px 1px;
}

.header-language {
    @media #{$xl} {
        margin-inline-start: 40px;
    }
}

.tp-sticky-one {
    display: none;

    &.header-sticky {
        display: block;
        @media #{$lg,$md,$xs} {
            display: none;
        }
    }
}

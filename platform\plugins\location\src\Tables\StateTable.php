<?php

namespace Bo<PERSON>ble\Location\Tables;

use Botble\Base\Facades\Html;
use Bo<PERSON>ble\Location\Models\Country;
use Bo<PERSON>ble\Location\Models\State;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\SelectBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class StateTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(State::class)
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('state.edit'),
                Column::make('country_id')
                    ->title(trans('plugins/location::state.country'))
                    ->alignStart(),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addHeaderAction(CreateHeaderAction::make()->route('state.create'))
            ->addActions([
                EditAction::make()->route('state.edit'),
                DeleteAction::make()->route('state.destroy'),
            ])
            ->addBulkAction(DeleteBulkAction::make()->permission('state.destroy'))
            ->addBulkChanges([
                NameBulkChange::make(),
                SelectBulkChange::make()
                    ->name('country_id')
                    ->title(trans('plugins/location::city.country'))
                    ->searchable()
                    ->choices(fn () => Country::query()->pluck('name', 'id')->all()),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'country_id',
                        'created_at',
                        'status',
                    ]);
            })
            ->onAjax(function () {
                $data = $this->table
                    ->eloquent($this->query())
                    ->editColumn('country_id', function (State $item) {
                        if (! $item->country_id && $item->country->name) {
                            return null;
                        }

                        return Html::link(route('country.edit', $item->country_id), $item->country->name);
                    })
                    ->filter(function (Builder $query) {
                        $keyword = $this->request->input('search.value');

                        if (! $keyword) {
                            return $query;
                        }

                        return $query->where(function (Builder $query) use ($keyword): void {
                            $query
                                ->where('id', $keyword)
                                ->orWhere('name', 'LIKE', '%' . $keyword . '%')
                                ->orWhereHas('country', function (Builder $subQuery) use ($keyword) {
                                    return $subQuery
                                        ->where('name', 'LIKE', '%' . $keyword . '%');
                                });
                        });
                    });

                return $this->toJson($data);
            });
    }
}

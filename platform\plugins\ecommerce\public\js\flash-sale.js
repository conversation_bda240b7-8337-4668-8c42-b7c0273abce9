$((function(){$(document).on("click",".list-search-data .selectable-item",(function(e){e.preventDefault();var t=$(e.currentTarget),a=t.closest(".box-search-advance").find("input[type=hidden]"),r=[];if($.each(a.val().split(","),(function(e,t){t&&""!==t&&(r[e]=parseInt(t))})),$.inArray(t.data("id"),r)<0){a.val()?a.val("".concat(a.val(),",").concat(t.data("id"))):a.val(t.data("id"));var c=$(document).find("#selected_product_list_template").html().replace(/__name__/gi,t.data("name")).replace(/__id__/gi,t.data("id")).replace(/__index__/gi,r.length).replace(/__url__/gi,t.data("url")).replace(/__image__/gi,t.data("image")).replace(/__price__/gi,t.data("price")).replace(/__attributes__/gi,t.find("a span").text());t.closest(".box-search-advance").find(".list-selected-products").show(),t.closest(".box-search-advance").find(".list-selected-products").append(c)}t.closest(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-search-advanced"]',(function(e){var t=$(e.currentTarget),a=t.closest(".box-search-advance").find(".card");a.show(),a.addClass("active"),0===a.find(".card-body").length&&(Botble.showLoading(a),$.ajax({url:t.data("bb-target"),type:"GET",success:function(e){e.error?Botble.showError(e.message):a.html(e.data)},error:function(e){Botble.handleError(e)},complete:function(){Botble.hideLoading(a)}}))})),$(document).on("keyup",'[data-bb-toggle="product-search-advanced"]',(function(e){var t=$(e.currentTarget),a=t.closest(".box-search-advance").find(".card");setTimeout((function(){Botble.hideLoading(a),$.ajax({url:"".concat(t.data("bb-target"),"?keyword=").concat(t.val()),type:"GET",success:function(e){e.error?Botble.showError(e.message):a.html(e.data)},error:function(e){Botble.handleError(e)},complete:function(){Botble.hideLoading(a)}})}),500)})),$(document).on("click",".box-search-advance .page-link",(function(e){e.preventDefault();var t=$(e.currentTarget).closest(".box-search-advance").find('[data-bb-toggle="product-search-advanced"]');if(!t.closest(".page-item").hasClass("disabled")&&t.data("bb-target")){var a=t.closest(".box-search-advance").find(".card");Botble.showLoading(a),$.ajax({url:"".concat($(e.currentTarget).prop("href"),"&keyword=").concat(t.val()),type:"GET",success:function(e){e.error?Botble.showError(e.message):a.html(e.data)},error:function(e){Botble.handleError(e)},complete:function(){Botble.hideLoading(a)}})}})),$(document).on("click","body",(function(e){var t=$(".box-search-advance");t.is(e.target)||0!==t.has(e.target).length||t.find(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-delete-item"]',(function(e){e.preventDefault();var t=$(e.currentTarget).closest(".box-search-advance").find("input[type=hidden]"),a=t.val().split(",");$.each(a,(function(e,t){t=t.trim(),_.isEmpty(t)||(a[e]=parseInt(t))}));var r=a.indexOf($(e.currentTarget).data("bb-target"));r>-1&&delete a[r],t.val(a.join(",")),$(e.currentTarget).closest(".list-group").find(".list-group-item").length<2&&$(e.currentTarget).closest(".list-selected-products").hide(),$(e.currentTarget).closest(".list-group").find(".list-group-item[data-product-id=".concat($(e.currentTarget).data("bb-target"),"]")).remove()}))}));
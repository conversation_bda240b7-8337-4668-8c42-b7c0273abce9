<?php

use <PERSON><PERSON><PERSON>\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Ecommerce\Http\Controllers\Fronts\PublicCartController;
use Bo<PERSON>ble\Ecommerce\Http\Middleware\CheckCartEnabledMiddleware;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;

Theme::registerRoutes(function (): void {
    Route::middleware(CheckCartEnabledMiddleware::class)
        ->controller(PublicCartController::class)
        ->prefix(EcommerceHelper::getPageSlug('cart'))
        ->name('public.')
        ->group(function (): void {
            Route::get('/', 'index')->name('cart');
            Route::post('add-to-cart', 'store')->name('cart.add-to-cart');
            Route::post('update', 'update')->name('cart.update');
            Route::get('remove/{id}', 'destroy')->name('cart.remove');
            Route::get('destroy', 'empty')->name('cart.destroy');
        });
});

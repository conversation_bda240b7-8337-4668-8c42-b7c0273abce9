<?php

namespace Bo<PERSON>ble\Ecommerce\Forms;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Ecommerce\Enums\GlobalOptionEnum;
use Botble\Ecommerce\Http\Requests\GlobalOptionRequest;
use Botble\Ecommerce\Models\GlobalOption;

class GlobalOptionForm extends FormAbstract
{
    public function setup(): void
    {
        Assets::addScripts(['jquery-ui'])
            ->addScriptsDirectly('vendor/core/plugins/ecommerce/js/global-option.js');

        $this
            ->model(GlobalOption::class)
            ->setValidatorClass(GlobalOptionRequest::class)
            ->add('name', TextField::class, NameFieldOption::make())
            ->add('option_type', 'customSelect', [
                'label' => trans('plugins/ecommerce::product-option.option_type'),
                'required' => true,
                'attr' => ['class' => 'form-control option-type'],
                'choices' => GlobalOptionEnum::options(),
            ])
            ->add('required', 'onOff', [
                'label' => trans('plugins/ecommerce::product-option.required'),
                'default_value' => false,
            ])
            ->setBreakFieldPoint('option_type')
            ->addMetaBoxes([
                'product_options_box' => [
                    'id' => 'product_options_box',
                    'title' => trans('plugins/ecommerce::product-option.option_value'),
                    'content' => view(
                        'plugins/ecommerce::product-options.option-admin',
                        ['values' => $this->model->values->sortBy('order')]
                    )->render(),
                ],
            ]);
    }
}

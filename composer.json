{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2|^8.3", "ext-curl": "*", "ext-gd": "*", "ext-json": "*", "ext-pdo": "*", "ext-zip": "*", "botble/api": "^2.0.0", "botble/data-synchronize": "^1.0", "botble/get-started": "*@dev", "botble/installer": "*@dev", "botble/menu": "*@dev", "botble/optimize": "*@dev", "botble/page": "*@dev", "botble/platform": "*@dev", "botble/plugin-management": "*@dev", "botble/revision": "*@dev", "botble/seo-helper": "*@dev", "botble/shortcode": "*@dev", "botble/sitemap": "*@dev", "botble/slug": "*@dev", "botble/theme": "*@dev", "botble/widget": "*@dev", "doctrine/dbal": "^4.2", "guzzlehttp/guzzle": "^7.9", "knuckleswtf/scribe": "^5.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.10", "predis/predis": "^2.3", "wikimedia/composer-merge-plugin": "^2.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "botble/dev-tool": "^1.0.2", "botble/git-commit-checker": "^2.1", "driftingly/rector-laravel": "^2.0", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1", "rector/rector": "^2.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "Aws\\Script\\Composer\\Composer::removeUnusedServices", "@php artisan cms:publish:assets"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["./platform/plugins/*/composer.json", "./platform/themes/*/composer.json"], "recurse": false, "replace": false, "ignore-duplicates": false, "merge-dev": false, "merge-extra": false, "merge-extra-deep": false, "merge-scripts": false}, "aws/aws-sdk-php": ["S3", "Ses", "Translate"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "pestphp/pest-plugin": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "./platform/core"}, {"type": "path", "url": "./platform/packages/*"}]}
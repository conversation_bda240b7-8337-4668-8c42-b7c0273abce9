<?php

namespace Bo<PERSON>ble\Ecommerce\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\DescriptionFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\Ecommerce\Http\Requests\SpecificationGroupRequest;
use Botble\Ecommerce\Models\SpecificationGroup;

class SpecificationGroupForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(SpecificationGroup::class)
            ->setValidatorClass(SpecificationGroupRequest::class)
            ->add(
                'name',
                TextField::class,
                NameFieldOption::make()
                    ->required(),
            )
            ->add(
                'description',
                TextareaField::class,
                DescriptionFieldOption::make()
            );
    }
}

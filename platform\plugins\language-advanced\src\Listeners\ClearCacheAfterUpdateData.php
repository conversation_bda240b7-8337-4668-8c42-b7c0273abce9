<?php

namespace Bo<PERSON>ble\LanguageAdvanced\Listeners;

use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Botble\Support\Services\Cache\Cache;

class ClearCacheAfterUpdateData
{
    public function handle(UpdatedContentEvent $event): void
    {
        if (! $event->data instanceof BaseModel) {
            return;
        }

        Cache::make($event->data::class)->flush();
    }
}

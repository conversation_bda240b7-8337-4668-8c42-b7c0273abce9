@use '../utils' as *;

.tpproduct-details {
    &__nab {
        @media #{$xs} {
            padding-inline-end: 0;
        }

        & .nav-link {
            padding: 0;
            border-color: transparent;
            margin-bottom: 20px;

            & img {
                height: 80px;
                width: 70px;
                object-fit: cover;
                border-radius: 6px;
                border-color: transparent;
            }

            &.nav-link.active,
            .nav-pills .show > .nav-link {
                color: #fff;
                background-color: transparent;
            }
        }

        & .tab-pane {
            & img {
                max-width: 100%;
                border-radius: 6px;
            }
        }
    }

    &__sticky {
        margin-inline-start: -25px;
        position: sticky;
        top: 150px;
        @media #{$lg} {
            margin-inline-start: -50px;
        }
        @media #{$md,$xs} {
            margin-inline-start: 0px;
            margin-top: 20px;
        }
        @media #{$sm} {
            margin-inline-start: 0px;
            margin-top: 20px;
        }
    }

    &__tag {
        display: inline-block;
        font-size: 14px;
        color: var(--tp-text-primary);
        font-weight: 600;
        line-height: 1;
        padding: 4px 8px;
        background-color: rgba($color: var(--tp-text-primary), $alpha: 0.1);
        border-radius: 2px;
    }

    &__rating {
        margin-inline-start: 20px;

        & a {
            font-size: 14px;
            color: var(--tp-common-yellow);

            & i {
                margin-inline-start: -3px;
            }
        }
    }

    &__reviewers {
        font-size: 14px;
        margin-inline-start: 5px;
        color: var(--tp-text-secondary);
        cursor: pointer;

        &:hover {
            color: var(--tp-text-primary);
        }
    }

    &__title {
        font-size: 26px;
        font-weight: 600;
        color: var(--tp-text-body);
        margin-inline-end: 15px;
        @media #{$lg} {
            font-size: 20px;
        }
        @media #{$xs} {
            font-size: 23px;
        }
    }

    &__stock {
        font-size: 12px;
        font-weight: 600;
        color: var(--tp-text-primary);
        border: 1px solid var(--tp-border-1);
        border-radius: 2px;
        padding: 4px 9px;
        line-height: 1;
    }

    &__price {
        & del {
            font-size: 20px;
            font-weight: 600;
            color: var(--tp-text-body);
        }

        & span {
            color: var(--tp-text-primary);
            font-weight: 600;
            font-size: 36px;
            @media #{$lg,$xs} {
                font-size: 26px;
            }
        }
    }

    &__pera {
        & p {
            font-size: 16px;
            color: var(--tp-text-secondary);
            margin-bottom: 23px;
            @media #{$lg} {
                font-size: 12px;
            }

            & br {
                @media #{$md,$xs} {
                    display: none;
                }
            }
        }
    }

    &__quantity {
        border: 1px solid var(--tp-border-1);
        border-radius: 6px;
        display: inline-block;
        padding: 16px 28px;
        @media #{$lg,$md} {
            padding: 11px 23px;
        }
        @media #{$xs} {
            padding: 11px 10px;
        }

        & input {
            border: none;
            width: 25px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
        }

        & .cart-minus,
        & .cart-plus {
            color: var(--tp-grey-7);
            cursor: pointer;
        }
    }

    &__cart {
        @media #{$xs} {
            margin-inline-start: 10px;
        }

        & button {
            font-size: 16px;
            font-weight: 600;
            background-color: var(--tp-text-primary);
            color: var(--tp-common-white);
            display: inline-block;
            padding: 22px 36px;
            line-height: 1;
            border-radius: 6px;
            @include transition(0.3s);

            &:hover {
                background-color: var(--tp-text-body);
            }

            @media #{$lg,$md} {
                padding: 17px 20px;
            }
            @media #{$xs} {
                padding: 18px 14px;
                font-size: 13px;
            }
        }
    }

    &__wishlist {
        height: 60px;
        width: 60px;
        text-align: center;
        border: 1px solid var(--tp-border-1);
        display: inline-block;
        line-height: 60px;
        color: var(--tp-grey-7);
        border-radius: 6px;
        @include transition(0.3s);
        @media #{$lg,$md} {
            height: 50px;
            width: 50px;
            line-height: 50px;
        }
        @media #{$xs} {
            height: 50px;
            width: 50px;
            line-height: 50px;
            margin-inline-start: 10px;
        }

        &:hover {
            color: var(--tp-common-white);
            background-color: var(--tp-text-primary);
            border: 1px solid var(--tp-text-primary);
        }

        & a {
            display: block;
        }
    }

    &__information {
        & p {
            margin-bottom: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--tp-text-body);
            display: inline-block;
            margin-inline-end: 5px;
            margin-bottom: 5px;
        }

        & span {
            color: var(--tp-text-secondary);
            font-weight: 400;
            font-size: 16px;

            & a {
                &:hover {
                    color: var(--tp-text-primary);
                }
            }
        }

        & a {
            color: var(--tp-text-secondary);
            margin-inline-end: 12px;
            font-size: 16px;

            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__condation {
        @media #{$md} {
            margin-inline-start: 0px;
        }
        @media #{$xs} {
            margin-top: 40px;
            margin-inline-start: 0px;
        }
        @media #{$sm} {
            margin-top: 40px;
            margin-inline-start: 0px;
        }

        & ul {
            & li {
                list-style: none;
                margin-bottom: 20px;
            }
        }

        &-item {
            border: 1px solid var(--tp-border-1);
            border-radius: 6px;
            padding: 11px 25px;
            @include transition(0.3s);
            @media #{$lg} {
                padding: 11px 8px;
            }

            &:hover {
                & .tpproduct-details__img-hover {
                    transform: translate3d(0, -5px, 0);
                }
            }
        }
    }

    &__img-hover {
        margin-inline-end: 20px;
        flex: 0 0 auto;
        transform: translate3d(0, 0, 0);
        transition: transform 0.3s cubic-bezier(0.21, 0.6, 0.44, 2.18);
        @media #{$lg} {
            margin-inline-end: 10px;
        }
    }

    &__condation-text {
        & p {
            font-size: 14px;
            margin-bottom: 0;
            color: var(--tp-text-secondary);

            & br {
                @media #{$lg,$sm} {
                    display: none;
                }
            }
        }
    }

    &__nav {
        & .nav-tabs {
            border-bottom: 1px solid var(--tp-border-1);
            display: flex;
            justify-content: center;
            padding-bottom: 15px;

            & .nav-links {
                font-size: 16px;
                font-weight: 600;
                color: var(--tp-text-secondary);
                margin: 0 32px;
                @media #{$xs} {
                    font-size: 14px;
                    margin: 0 12px;
                }
                @media #{$sm} {
                    margin: 0 15px;
                }

                &.active {
                    color: var(--tp-text-body);
                }
            }
        }
    }

    &__list-img {
        padding-inline-end: 60px;
        @media #{$md,$xs,$sm} {
            padding-inline-end: 0;
        }
    }

    &__list-img-item {
        margin-bottom: 10px;

        & img {
            max-width: 100%;
            border-radius: 6px;
        }
    }
}

.latest-comments {
    & ul {
        & li {
            list-style: none;
        }
    }
}

.comments-box {
    margin-bottom: 50px;
}

.user-rating {
    & ul {
        & li {
            display: inline-block;
            color: var(--tp-common-yellow);
        }
    }
}

.comments-text {
    & span,
    p {
        color: var(--tp-text-secondary);
        font-size: 16px;
    }
}

.comment-title {
    & p {
        color: var(--tp-text-secondary);
        font-size: 16px;
    }
}

.comment-rating {
    & span {
        font-weight: 500;
        margin-inline-end: 5px;
    }

    & ul {
        & li {
            display: inline-block;
            color: var(--tp-common-yellow);
        }
    }
}

.comment-input {
    & textarea {
        width: 100%;
        height: 120px;
        margin-bottom: 20px;
        border: 1px solid #e7e7e7;
        padding: 20px;

        &:focus-visible {
            outline: 0;
            border: 1px solid var(--tp-text-primary) !important;
        }
    }

    & input {
        width: 100%;
        height: 50px;
        resize: none;
        outline: 0;
        border: 1px solid #e7e7e7;
        padding: 8px 20px;
        margin-bottom: 30px;

        &:focus {
            border: 1px solid var(--tp-text-primary);
        }
    }
}

.comments-avatar {
    flex: 0 0 auto;
}

.table .add-info {
    font-size: 18px;
    font-weight: 500;
}

.table {
    & td {
        padding: 18px;
    }
}

.shop-left-right {
    @media #{$xxl,$xl,$lg,$md,$xs} {
        margin-inline-start: 0;
        margin-inline-end: 0;
    }
}

.tp-comments-title {
    font-size: 24px;

    @media #{$xs} {
        font-size: 18px;
    }
}

.tp-content-tab {
    & p {
        font-size: 16px;
        color: var(--tp-text-secondary);
    }
}

.pro-submit {
    background-color: var(--tp-text-primary);
    color: var(--tp-common-white);
}

.tprelated {
    &__arrow {
        @media #{$xs} {
            justify-content: start !important;
        }

        & i {
            height: 50px;
            width: 50px;
            line-height: 50px;
            text-align: center;
            background-color: var(--tp-grey-2);
            border-radius: 6px;
            @include transition(0.3s);

            &:hover {
                background-color: var(--tp-text-primary);
                color: var(--tp-common-white);
            }
        }
    }

    &__prv {
        margin-inline-end: 15px;
    }
}

.related-product-border {
    border-top: 1px solid var(--tp-border-1);
}

.comments-text {
    width: 100%;
}

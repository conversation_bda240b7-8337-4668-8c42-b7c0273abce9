@use '../utils' as *;

.mobile-menu-container {
    .mobile-menu-nav {
        float: left;
        width: 100%;
        background: none;
        margin-top: 0;
    }
}

.mobile-menu-container .mobile-menu-bar {
    padding: 0;
    min-height: auto;
    background: none;
    float: left;
    width: 100%;
    position: relative;
    z-index: 999999;

    * {
        box-sizing: content-box;
    }
}

.mobile-menu-container .mobile-menu-nav > ul {
    padding: 0;
    margin: 0;
    width: 100%;
    list-style-type: none;
    display: block !important;

    li {
        position: relative;
        float: left;
        width: 100%;
    }
}

.mobile-menu-container .mobile-menu-nav ul li li a {
    width: 80%;
    padding: 10px 10%;
    text-shadow: none !important;
    visibility: visible;
}

.mobile-menu-container .mobile-menu-nav ul li a {
    display: block;
    float: left;
    width: 100%;
    padding: 10px 0;
    margin: 0;
    text-align: left;
    color: #fff;
    font-size: 16px;
    line-height: 1.5;
    font-weight: 500;
    border-bottom: 1px solid #fff3;
    text-decoration: none;
    text-transform: capitalize !important;

    &:hover {
        color: var(--tp-text-primary);
    }
}

.mobile-menu-container .mobile-menu-nav ul li a.mobile-menu-exapand {
    margin-top: 8px;
    padding: 0 !important;
    line-height: 14px;
    border: 1px solid #fff3 !important;
    height: 26px;
    width: 26px;
    text-align: center;
    position: absolute;
    right: 0;
    line-height: 26px;
    color: #fff;
    top: 0;
    z-index: 2;
    background: 0 0;
    font-weight: 400;

    &:hover {
        background: var(--clr-theme-1);
        color: var(--tp-text-primary);
        border-color: var(--clr-theme-1);
    }
}

.mobile-menu-container .mobile-menu-nav ul li > a > i {
    display: none;
}

.mobile-menu-container .mobile-menu-nav ul li > a.mobile-menu-exapand i {
    display: inline-block;
    font-size: 14px;
}

.mobile-menu-container .mobile-menu-nav > ul > li:first-child > a {
    border-top: 0;
}

.mobile-menu-container .mobile-menu-nav ul li a.mobile-menu-exapand.mobile-menu-clicked {
    color: var(--tp-heading-secondary);
}

.mobile-menu-container .mobile-menu-nav ul li a.mobile-menu-exapand.mobile-menu-clicked i {
    transform: rotate(45deg);
    color: var(--tp-text-primary);
}

.mobile-menu-container .mobile-menu-nav {
    & ul {
        & .home-menu-style {
            & li {
                & a {
                    text-align: center;
                }
            }
        }

        & li {
            & a {
                &:hover {
                    & img {
                        transform: scale(0.92);
                    }
                }

                & img {
                    @include transition(0.3s);
                    width: 100%;
                    margin-bottom: 5px;
                }
            }
        }
    }
}

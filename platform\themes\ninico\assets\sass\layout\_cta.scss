@use '../utils' as *;

.tpcta {
    padding: 58px 60px;
    border-radius: 6px;
    margin-inline-start: 30px;
    position: relative;
    z-index: 1;
    @media #{$xl} {
        padding: 30px 20px;
    }
    @media #{$lg} {
        padding: 20px 15px;
    }
    @media #{$md} {
        margin-inline-start: 0;
    }
    @media #{$xs} {
        padding: 40px 20px;
        margin-inline-start: 0;
    }

    &::before {
        position: absolute;
        content: '';
        height: 75px;
        width: 75px;
        border-radius: 50%;
        background-color: var(--tp-common-white);
        z-index: -1;
        top: 40px;
        inset-inline-end: 135px;
    }

    & p {
        color: #9a9387;
        font-size: 14px;
    }

    &__subtitle {
        font-size: 20px;
        font-weight: 400;
        color: var(--tp-text-primary);
        margin-bottom: 5px;
    }

    &__title {
        font-size: 26px;
        font-weight: 600;
        @media #{$xs} {
            font-size: 22px;
        }
    }

    &__input {
        &-icon {
            position: absolute;
            top: 17px;
            inset-inline-start: 30px;

            & i {
                font-size: 16px;
            }
        }

        & input {
            border: none;
            border-radius: 6px;
            height: 60px;
            width: 100%;
            padding: 10px 30px 10px 52px;

            &::placeholder {
                color: #b0b0b0;
                font-size: 16px;
            }
        }
    }

    &__btn {
        & button {
            background-color: var(--tp-text-primary);
            color: var(--tp-common-white);
            font-size: 16px;
            font-weight: 700;
            width: 100%;
            padding: 17px 0;
            border-radius: 6px;
            margin-inline-end: 15px;

            &:hover {
                & i {
                    animation: iconarrow 0.4s linear;
                }
            }

            & span {
                margin-inline-start: 15px;
            }
        }
    }
}

.tptrack {
    &__item-icon {
        flex: 0 0 auto;
        height: 50px;
        width: 50px;
        text-align: center;
        line-height: 50px;
        background-color: var(--tp-common-white);
        border-radius: 6px;
        margin-inline-end: 20px;
    }

    &__thumb {
        & img {
            border-radius: 8px 8px 0 0;
            width: 100%;
        }
    }

    &__content {
        padding: 50px;
        border-radius: 0 0 8px 8px;
        @media #{$xs} {
            padding: 20px 15px;
        }
    }

    &__item-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--tp-text-body);
    }

    &__item-content {
        & p {
            font-size: 14px;
            color: var(--tp-text-secondary);
            line-height: 24px;
            margin-bottom: 0;
        }
    }

    &__id,
    &__email {
        div {
            position: relative;
        }

        & span {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            inset-inline-start: 30px;
            font-size: 16px;
        }

        & input {
            width: 100%;
            height: 60px;
            border: none;
            background-color: var(--tp-common-white);
            padding: 10px 60px;
            border-radius: 6px;

            &::placeholder {
                font-size: 14px;
                color: var(--tp-grey-9);
            }
        }
    }

    &__submition {
        background: var(--tp-text-primary);
        border-radius: 6px;
        color: var(--tp-common-white);
        display: inline-block;
        font-size: 16px;
        font-weight: 600;
        line-height: 1;
        margin-bottom: 0;
        padding: 22px 50px;
        text-align: center;
        touch-action: manipulation;
        transition: all 0.3s ease 0s;
        vertical-align: middle;
        white-space: nowrap;
        width: 100%;

        & i {
            margin-inline-start: 20px;
        }

        &:hover {
            background-color: var(--bs-dark);
        }
    }
}

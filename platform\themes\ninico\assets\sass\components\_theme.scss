@use '../utils' as *;

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--tp-ff-body);
    font-size: 18px;
    font-weight: 400;
    color: var(--tp-text-body);
    line-height: 26px;
    overflow-x: hidden !important;
}

a {
    text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--tp-ff-heading);
    color: var(--tp-header-1);
    margin-top: 0px;
    font-weight: var(--tp-fw-sbold);
    line-height: 1.2;
    @include transition(0.3s);
}

h1 {
    font-size: 40px;
}

h2 {
    font-size: 32px;
}

h3 {
    font-size: 28px;
}

h4 {
    font-size: 24px;
}

h5 {
    font-size: 20px;
}

h6 {
    font-size: 16px;
}

ul {
    margin: 0px;
    padding: 0px;
}

p {
    font-family: var(--tp-ff-p);
    font-size: 18px;
    font-weight: 400;
    color: var(--tp-text-body);
    margin-bottom: 15px;
    line-height: 26px;
}

a,
.btn,
button,
p,
i,
input,
select,
textarea,
li,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
    @include transition(0.3s);
}

a:focus,
.button:focus {
    text-decoration: none;
    outline: none;
}

a:focus,
a:hover {
    color: inherit;
    text-decoration: none;
}

a {
    text-decoration: none;
}

a,
button {
    color: inherit;
    outline: none;
    border: none;
    background: transparent;
}

button:hover {
    cursor: pointer;
}

button:focus {
    outline: 0;
}

.uppercase {
    text-transform: uppercase;
}

.capitalize {
    text-transform: capitalize;
}

input {
    outline: none;
}

input[type='color'] {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: none;
    border: 0;
    cursor: pointer;
    height: 100%;
    width: 100%;
    padding: 0;
    border-radius: 50%;
}

*::-moz-selection {
    background: var(--tp-common-black);
    color: var(--tp-common-white);
    text-shadow: none;
}

::-moz-selection {
    background: var(--tp-common-black);
    color: var(--tp-common-white);
    text-shadow: none;
}

::selection {
    background: var(--tp-text-primary);
    color: var(--tp-common-white);
    text-shadow: none;
}

*::-moz-placeholder {
    color: var(--tp-common-black);
    font-size: var(--tp-fz-body);
    opacity: 1;
}

*::placeholder {
    color: var(--tp-common-black);
    font-size: var(--tp-fz-body);
    opacity: 1;
}

.w-img {
    & img {
        width: 100%;
    }
}

.m-img {
    & img {
        max-width: 100%;
    }
}

.fix {
    overflow: hidden;
}

.clear {
    clear: both;
}

.z-index-1 {
    z-index: 1;
}

.z-index-11 {
    z-index: 11;
}

.overflow-y-visible {
    overflow-x: hidden;
    overflow-y: visible;
}

.p-relative {
    position: relative;
}

.p-absolute {
    position: absolute;
}

.include-bg {
    @include background();
}

.gx-6 {
    --bs-gutter-x: 80px;
    @media #{$xl,$lg} {
        --bs-gutter-x: 30px;
    }
    @media #{$md,$sm} {
        --bs-gutter-x: 30px;
    }
}

.f-right {
    float: right;
}

/* scrollUp */
.scroll-top {
    width: 40px;
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: -10%;
    inset-inline-end: 50px;
    font-size: 16px;
    border-radius: 6px;
    z-index: 99;
    color: var(--tp-common-white);
    text-align: center;
    cursor: pointer;
    background: var(--tp-text-primary);
    transition: 1s ease;
    border: none;
    @media #{$xs} {
        inset-inline-end: 30px;
        width: 40px;
        height: 40px;
        line-height: 40px;
    }
}

.scroll-top.open {
    bottom: 30px;
}

.scroll-top::after {
    position: absolute;
    z-index: -1;
    content: '';
    top: 100%;
    inset-inline-start: 5%;
    height: 10px;
    width: 90%;
    opacity: 1;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 80%);
}

.scroll-top:hover {
    background: var(--tp-text-body);
}

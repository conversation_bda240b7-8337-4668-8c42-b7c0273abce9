<?php

namespace Bo<PERSON><PERSON>\AuditLog\Providers;

use Bo<PERSON><PERSON>\AuditLog\Events\AuditHandlerEvent;
use <PERSON><PERSON><PERSON>\AuditLog\Listeners\AuditHandlerListener;
use Bo<PERSON><PERSON>\AuditLog\Listeners\CreatedContentListener;
use <PERSON><PERSON><PERSON>\AuditLog\Listeners\CustomerLoginListener;
use Bo<PERSON>ble\AuditLog\Listeners\CustomerLogoutListener;
use Bo<PERSON>ble\AuditLog\Listeners\CustomerRegistrationListener;
use Botble\AuditLog\Listeners\DeletedContentListener;
use Botble\AuditLog\Listeners\LoginListener;
use Bo<PERSON>ble\AuditLog\Listeners\UpdatedContentListener;
use Botble\Base\Events\CreatedContentEvent;
use Botble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        AuditHandlerEvent::class => [
            AuditHandlerListener::class,
        ],
        Login::class => [
            LoginListener::class,
            CustomerLoginListener::class,
        ],
        Logout::class => [
            CustomerLogoutListener::class,
        ],
        Registered::class => [
            CustomerRegistrationListener::class,
        ],
        UpdatedContentEvent::class => [
            UpdatedContentListener::class,
        ],
        CreatedContentEvent::class => [
            CreatedContentListener::class,
        ],
        DeletedContentEvent::class => [
            DeletedContentListener::class,
        ],
    ];
}

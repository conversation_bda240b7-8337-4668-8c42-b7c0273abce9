#rv_media_body {
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 8rem);
    min-height: 500px;
}

.navbar .dropdown-menu-right {
    right: 0;
    left: auto;
}

.rv-media-container {
    width: 100%;
    min-height: 100%;
    height: 100%;
    font-size: 13px;
    overflow: auto;
    display: flex;
    flex-grow: 1;
    flex-direction: column;

    .form-add-folder {
        .input-group-text {
            padding: 0;
        }
    }

    .rv-media-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;
        flex-grow: 1;

        .rv-media-aside,
        .rv-media-main-wrapper {
            @include lib-css(align-items, stretch);
        }

        .rv-media-main-wrapper {
            @include lib-css(flex-grow, 1);
            display: flex;
            @include lib-css(flex-direction, column);

            .rv-media-main {
                @include lib-css(flex-grow, 1);
                @include lib-css(flex-direction, row);
                display: flex;
                height: 200px;
            }

            .rv-dropdown-actions {
                .dropdown-toggle:after {
                    display: none;
                }
            }
        }
    }

    .rv-media-items,
    .rv-media-details {
        @include lib-css(align-items, stretch);
        display: flex;
        overflow: auto;
        position: relative;
    }

    .rv-media-items {
        @include lib-css(flex-grow, 1);
        @include lib-css(flex-direction, column);
        @include lib-css(user-select, none);

        .rv-media-item {
            overflow: hidden;
            background-color: var(--bb-body-bg);
        }
    }

    .rv-media-details {
        width: 250px;
        min-width: 250px;
        border-left: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
        @include lib-css(flex-direction, column);
        @include lib-css(transition, all 0.3s ease-in-out);
        overflow: hidden;
        overflow-y: auto;

        .rv-media-thumbnail,
        .rv-media-description {
            width: 250px;
            min-width: 250px;
        }

        .rv-media-thumbnail {
            border-bottom: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
            overflow: visible;

            svg {
                width: 6rem;
                height: 6rem;
            }

            img {
                max-width: 90%;
            }
        }

        .rv-media-description {
            padding: 10px;
            position: relative;

            .rv-media-name {
                > p {
                    font-weight: 700;
                    margin: 0;
                }

                + .rv-media-name {
                    margin-top: 5px;
                }
            }
        }
    }

    #media_details_collapse:checked ~ .rv-media-main-wrapper .rv-media-details {
        width: 0;
        min-width: 0;
        border: 0 none;
    }

    #media_details_collapse:checked ~ .rv-media-main-wrapper .rv-media-tools .collapse-panel {
        svg {
            @include lib-css(transform, rotate(-180deg));
        }
    }

    @media (max-width: $screen-md-max) {
        .rv-media-details {
            width: 220px;
            min-width: 220px;

            .rv-media-thumbnail,
            .rv-media-description {
                width: 220px;
                min-width: 220px;
            }
        }
    }

    @media (max-width: $screen-sm-max) {
        .rv-media-wrapper .rv-media-main-wrapper {
            width: 100%;
        }

        .rv-media-details {
            display: none !important;
        }
    }
}

.media-download-popup {
    position: absolute;
    bottom: 0;
    right: 24px;
}

li.context-menu-item.context-menu-icon-updated.media-action-trash {
    color: rgba(var(--bb-danger-rgb), 1) !important;
}

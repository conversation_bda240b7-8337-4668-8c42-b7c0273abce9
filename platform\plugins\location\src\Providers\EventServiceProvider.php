<?php

namespace Bo<PERSON>ble\Location\Providers;

use Bo<PERSON><PERSON>\Location\Events\ImportedCityEvent;
use Bo<PERSON>ble\Location\Events\ImportedCountryEvent;
use Bo<PERSON><PERSON>\Location\Events\ImportedStateEvent;
use Bo<PERSON>ble\Location\Listeners\CreateCityTranslationListener;
use Botble\Location\Listeners\CreateCountryTranslationListener;
use Botble\Location\Listeners\CreateStateTranslationListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as BaseServiceProvider;

class EventServiceProvider extends BaseServiceProvider
{
    protected $listen = [
        ImportedCountryEvent::class => [
            CreateCountryTranslationListener::class,
        ],
        ImportedStateEvent::class => [
            CreateStateTranslationListener::class,
        ],
        ImportedCityEvent::class => [
            CreateCityTranslationListener::class,
        ],
    ];
}

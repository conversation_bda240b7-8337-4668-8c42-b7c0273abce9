@use '../utils' as *;

.blog-btn {
    font-size: 16px;
    font-weight: 600;
    @media #{$xs} {
        font-size: 14px;
    }

    &:hover {
        color: var(--tp-text-primary);

        & i {
            animation: iconarrow 0.4s linear;
        }
    }
}

.blogitem {
    &:hover {
        & .blogitem__thumb {
            & img {
                transform: scale(1.1);
            }
        }
    }

    &__thumb {
        border-radius: 6px;

        & img {
            @include transition(1s);
            width: 100%;
            @media #{$xl, $lg} {
                max-width: 100%;
            }
        }
    }

    &__content {
        & ul {
            & li {
                list-style: none;
                display: inline-block;
                margin-inline-end: 35px;
                position: relative;
                @media #{$lg} {
                    margin-inline-end: 15px;
                }

                &::before {
                    position: absolute;
                    content: '';
                    top: 50%;
                    inset-inline-end: -19px;
                    height: 15px;
                    width: 2px;
                    background: #ececec;
                    transform: translateY(-50%);
                    @media #{$lg} {
                        inset-inline-end: -10px;
                    }
                }

                &:last-child {
                    &::before {
                        display: none;
                    }
                }

                & a {
                    font-size: 14px;
                    font-weight: 400;
                    color: var(--tp-text-body);

                    &:hover {
                        color: var(--tp-text-primary);
                    }
                }

                & .date-color {
                    color: var(--tp-text-secondary);
                }
            }
        }
    }

    &__title {
        font-size: 20px;
        font-weight: 600;
        @media #{$xl,$xs} {
            font-size: 16px;
        }
        @media #{$lg} {
            font-size: 15px;
        }

        & a {
            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__btn {
        & a {
            font-size: 14px;
            font-weight: 600;
            color: #cbcbcb;
            background-image: linear-gradient(var(--tp-text-primary), var(--tp-text-primary)),
                linear-gradient(var(--tp-text-primary), var(--tp-text-primary));
            display: inline;
            background-size:
                0% 1px,
                0 1px;
            background-position:
                100% 100%,
                0 90%;
            background-repeat: no-repeat;
            transition: background-size 0.4s linear;
            line-height: 1.4;

            &:hover {
                background-size:
                    0 1px,
                    100% 1px;
                color: var(--tp-text-primary);
            }
        }
    }
}

.tpblogborder {
    border-inline-end: 1px solid var(--tp-theme-7);
    @media #{$md} {
        border: none;
    }
}

.blog-main-box {
    margin-inline-end: -10px;
    @media #{$md,$xs} {
        margin-inline-end: 0;
    }
}

.postbox {
    &__thumb {
        & img {
            border-radius: 10px;
            max-width: 100%;
        }
    }

    &__wrapper {
        @media #{$lg,$md,$xs} {
            padding-inline-end: 0;
        }
    }

    &__meta {
        & span {
            margin-inline-end: 45px;
            color: #5b5757;
            font-weight: 600;
            font-size: 14px;
            @media #{$lg} {
                margin-inline-end: 18px;
            }
            @media #{$xs} {
                margin-inline-end: 15px;
                font-size: 12px;
            }

            & i {
                margin-inline-end: 5px;
                color: var(--tp-text-primary);
            }

            & a {
                text-transform: uppercase;
                display: inline-block;
                color: #5b5757;
                font-weight: 600;

                &:hover {
                    color: var(--tp-text-primary);
                }
            }
        }
    }

    &__title {
        font-weight: 700;
        font-size: 36px;
        line-height: 1.25;
        @media #{$lg} {
            font-size: 30px;
        }
        @media #{$xs} {
            font-size: 24px;
        }

        & a {
            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__text {
        & p {
            font-size: 16px;
            @media #{$xs} {
                font-size: 14px;
            }
        }
    }

    & .play-btn {
        position: absolute;
        top: 50%;
        inset-inline-start: 50%;
        transform: translate(-50%, -50%);
        height: 88px;
        width: 88px;
        background: var(--tp-common-white);
        text-align: center;
        line-height: 88px;
        border-radius: 50%;
        color: var(--tp-text-primary);
        animation: tp-pulse-2 1.5s infinite;

        &:hover {
            background-color: var(--tp-text-primary);
            color: var(--tp-common-white);
        }
    }

    &__meta-img {
        & img {
            width: 100%;
            border-radius: 10px;
        }
    }

    &__tag {
        & span {
            font-size: 20px;
            margin-inline-end: 15px;
            font-weight: 700;
        }
    }

    &__social-tag {
        & span {
            font-size: 20px;
            font-weight: 700;
            margin-inline-end: 15px;
        }

        & a {
            margin-inline-end: 30px;
            font-size: 18px;

            &:last-child {
                margin-inline-end: 0;
            }
        }
    }

    &__tag-border {
        border-top: 1px solid var(--tp-border-1);
        border-bottom: 1px solid var(--tp-border-1);
        padding: 30px 0;
    }

    &__social-tag {
        text-align: end;

        & a {
            &:hover {
                color: var(--tp-text-primary);
            }
        }

        @media #{$lg,$md,$xs} {
            margin-top: 15px;
            text-align: start;
        }
    }

    &__content-area {
        & h4 {
            font-weight: 700;
            font-size: 36px;
            line-height: 1.25;
            @media #{$xl,$md} {
                font-size: 30px;
            }
            @media #{$lg} {
                font-size: 26px;
            }
            @media #{$xs} {
                font-size: 28px;
            }
        }

        & p {
            font-size: 16px;
            margin-bottom: 30px;
            @media #{$lg} {
                font-size: 15px;
            }

            & br {
                @media #{$lg,$md,$xs} {
                    display: none;
                }
            }
        }
    }

    &__text-list {
        & ul {
            & li {
                font-size: 15px;
                font-weight: 500;
                margin-bottom: 5px;
                list-style: none;

                &:last-child {
                    margin-bottom: 0;
                }

                & i {
                    margin-inline-end: 20px;
                    font-size: 18px;
                    color: var(--tp-text-primary);
                }
            }
        }
    }

    &__comment {
        border-bottom: 1px solid var(--tp-border-1);

        & ul {
            & li {
                margin-bottom: 10px;
                list-style: none;

                &.children {
                    margin-inline-start: 100px;
                    margin-bottom: 30px;
                    @media #{$xs} {
                        margin-inline-start: 0px;
                    }
                }
            }
        }

        &-form {
            margin-bottom: 20px;

            &-title {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 15px;
            }

            & p {
                color: var(--tp-text-secondary);
                font-size: 16px;
                margin-bottom: 60px;
            }
        }

        &-input {
            position: relative;
            margin-bottom: 30px;
            color: var(--tp-text-body);

            & span {
                font-weight: 600;
                color: var(--tp-common-black);
                margin-bottom: 12px;
                display: block;
            }

            & input,
            & textarea {
                height: 60px;
                padding: 0 20px;
                width: 100%;
                font-size: 14px;
                color: var(--tp-text-body);
                outline: none;
                border: 1px solid var(--tp-border-1);
                @include border-radius(7px);
                box-shadow: 0px 16px 24px rgba(189, 196, 205, 0.13);

                &:focus {
                    border: 1px solid var(--tp-text-primary);
                }

                &::placeholder {
                    color: var(--tp-text-secondary);

                    &:focus {
                        color: var(--tp-text-primary);
                    }
                }
            }

            & textarea {
                height: 175px;
                resize: none;
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }

        &-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 35px;
        }

        &-box {
            margin-bottom: 70px;
        }

        &-avater {
            & img {
                width: 80px;
                height: 80px;
                @include border-radius(50%);
            }
        }

        &-name {
            margin-bottom: 15px;

            & h5 {
                font-size: 16px;
                line-height: 1;
                margin-bottom: 8px;
                font-weight: 500;
            }

            & span {
                font-size: 14px;
                color: var(--tp-text-secondary);
            }
        }

        &-text {
            @media #{$xs} {
                margin-inline-start: 0;
                margin-top: 15px;
            }

            & p {
                font-size: 16px;
                color: var(--tp-text-secondary);
                margin-bottom: 15px;
                @media #{$lg} {
                    font-size: 15px;
                }
                @media #{$xs} {
                    font-size: 14px;
                }

                & br {
                    @media #{$lg,$md,$xs} {
                        display: none;
                    }
                }
            }
        }

        &-reply {
            margin-top: 10px;

            & a {
                display: inline-block;
                color: var(--tp-text-primary);
                background: var(--tp-common-white);
                line-height: 22px;
                padding: 4px 10px;
                font-weight: 500;
                font-size: 14px;
                -webkit-border-radius: 4px;
                -moz-border-radius: 4px;
                -o-border-radius: 4px;
                -ms-border-radius: 4px;
                border-radius: 4px;
                @include transition(0.2s);

                &:hover {
                    color: var(--tp-common-white);
                    background: var(--tp-text-primary);
                }
            }
        }

        &-agree {
            padding-inline-start: 5px;

            & input {
                margin: 0;
                appearance: none;
                -moz-appearance: none;
                display: block;
                width: 14px;
                height: 14px;
                background: var(--tp-common-white);
                border: 1px solid #b9bac1;
                outline: none;
                @include border-radius(4px);
                flex: 0 0 auto;
                @include transform(translateY(-1px));

                &:checked {
                    position: relative;
                    background-color: var(--tp-theme-1);
                    border-color: transparent;

                    &::after {
                        box-sizing: border-box;
                        content: '\f00c';
                        position: absolute;
                        font-family: var(--tp-ff-fontawesome);
                        font-size: 10px;
                        color: var(--tp-common-white);
                        top: 46%;
                        inset-inline-start: 50%;
                        @include transform(translate(-50%, -50%));
                    }
                }

                &:hover {
                    cursor: pointer;
                }
            }

            & label {
                padding-inline-start: 8px;
                color: var(--tp-text-1);
                line-height: 1;

                & a {
                    color: var(--tp-common-black);
                    font-weight: 600;
                    padding-inline-start: 4px;

                    &:hover {
                        color: var(--tp-theme-1);
                    }
                }

                &:hover {
                    cursor: pointer;
                }
            }
        }
    }
}

.postbox-nav {
    & button {
        position: absolute;
        inset-inline-start: 0px;
        top: 50%;
        z-index: 1;
        transform: translateY(-50%);

        &.postbox-slider-button-next {
            inset-inline-start: auto;
            inset-inline-end: 0px;
        }

        & i {
            height: 60px;
            width: 60px;
            line-height: 60px;
            background-color: var(--tp-common-white);
            color: var(--tp-text-body);
            @media #{$xs} {
                height: 40px;
                width: 40px;
                line-height: 40px;
            }

            &:hover {
                background-color: var(--tp-text-primary);
                color: var(--tp-common-white);
            }
        }
    }
}

.sidebar {
    &__wrapper {
        @media #{$lg,$md,$xs} {
            padding-inline-start: 0;
        }
    }

    &__widget {
        padding: 50px 30px 40px 30px;
        box-shadow: 0px 12px 10px rgba(238, 239, 240, 0.25);
        border: 1px solid var(--tp-border-1);
        border-radius: 6px;
    }

    &__widget-content {
        & ul {
            & li {
                margin-bottom: 15px;
                list-style: none;

                &:last-child {
                    margin-bottom: 0;
                }

                & a {
                    color: var(--tp-text-secondary);
                    font-weight: 600;
                    font-size: 12px;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border: 1px solid #f2f5fa;
                    padding: 9px 15px;
                    @include transition(0.2s);

                    &:hover {
                        color: var(--tp-text-primary);
                    }

                    & span {
                        display: block;
                        text-align: end;
                    }
                }
            }
        }
    }

    &__widget-title {
        font-size: 22px;
        font-weight: 600;
        color: var(--tp-text-body);
    }

    &__search-input-2 {
        & input {
            height: 60px;
            width: 100%;
            padding: 10px 60px 10px 20px;
            border: none;
            background-color: var(--tp-grey-8);

            &::placeholder {
                font-weight: 500;
                font-size: 12px;
                color: var(--tp-text-secondary);
                background-color: var(--tp-grey-8);
                text-transform: uppercase;
                letter-spacing: 1px;
            }
        }

        & button {
            position: absolute;
            top: 50%;
            inset-inline-end: 0;
            transform: translateY(-50%);
            padding: 16px 25px;
        }
    }
}

.rc {
    &__post-thumb {
        margin-inline-end: 20px;

        & img {
            height: 100px;
            width: 100px;
            object-fit: cover;
        }
    }

    &__meta {
        & span {
            font-weight: 600;
            font-size: 13px;
            color: var(--tp-text-secondary);
            letter-spacing: 1px;
            display: block;
            margin-bottom: 5px;
        }
    }

    &__post-title {
        font-weight: 500;
        font-size: 16px;
        color: #141515;
        line-height: 1.5;

        @media #{$lg,$xs} {
            font-size: 15px;
        }

        & a {
            @include transition(0.2s);

            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }
}

.tagcloud {
    & a {
        font-weight: 600;
        font-size: 12px;
        text-transform: uppercase;
        color: var(--tp-text-secondary);
        border: 1px solid #f2f4f6;
        padding: 10px 26px;
        display: inline-block;
        line-height: 1;
        margin: 5px 10px 5px 0;
        @include transition(0.3s);

        &:hover {
            color: var(--tp-common-white);
            background-color: var(--tp-text-primary);
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ads', function (Blueprint $table): void {
            $table->string('ads_type')->nullable();
            $table->string('google_adsense_slot_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ads', function (Blueprint $table): void {
            $table->dropColumn('ads_type');
            $table->dropColumn('google_adsense_slot_id');
        });
    }
};

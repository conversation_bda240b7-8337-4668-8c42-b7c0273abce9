$((function(){$(document).on("click",".btn-update-order",(function(e){e.preventDefault();var o=$(e.currentTarget);o.addClass("button-loading"),$.ajax({type:"POST",cache:!1,url:o.closest("form").prop("action"),data:o.closest("form").serialize(),success:function(e){e.error?Botble.showError(e.message):Botble.showSuccess(e.message),o.removeClass("button-loading")},error:function(e){Botble.handleError(e),o.removeClass("button-loading")}})})).on("click",".btn-trigger-send-order-recover-modal",(function(e){e.preventDefault(),$("#confirm-send-recover-email-button").data("action",$(e.currentTarget).data("action")),$("#send-order-recover-email-modal").modal("show")})).on("click","#confirm-send-recover-email-button",(function(e){e.preventDefault();var o=$(e.currentTarget);o.addClass("button-loading"),$.ajax({type:"POST",cache:!1,url:o.data("action"),success:function(e){e.error?Botble.showError(e.message):Botble.showSuccess(e.message),o.removeClass("button-loading"),$("#send-order-recover-email-modal").modal("hide")},error:function(e){Botble.handleError(e),o.removeClass("button-loading")}})})).on("click",'[data-bb-toggle="confirm-mark-as-completed-button"]',(function(e){e.preventDefault();var o=$(e.currentTarget),r=o.closest("form");$httpClient.make().withButtonLoading(o).post(r.prop("action"),r.serialize()).then((function(e){var o=e.data;o.error?Botble.showError(o.message):($("#mark-order-as-completed-modal").modal("hide"),Botble.showSuccess(o.message),o.data.next_url&&setTimeout((function(){return window.location.href=o.data.next_url}),2e3))}))}))}));
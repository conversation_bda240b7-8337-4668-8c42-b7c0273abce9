<?php

return [
    'name' => 'Activity Logs',
    'description' => 'View and delete your system activity logs',
    'created' => 'created',
    'updated' => 'updated',
    'deleted' => 'deleted',
    'logged in' => 'logged in',
    'logged out' => 'logged out',
    'changed password' => 'changed password',
    'updated profile' => 'updated profile',
    'attached' => 'attached',
    'shared' => 'shared',
    'to the system' => 'to the system',
    'to the customer portal' => 'to the customer portal',
    'of the system' => 'of the system',
    'menu' => 'menu',
    'post' => 'post',
    'page' => 'page',
    'category' => 'category',
    'tag' => 'tag',
    'user' => 'user',
    'contact' => 'contact',
    'backup' => 'backup',
    'custom-field' => 'custom field',
    'widget_audit_logs' => 'Activity Logs',
    'action' => 'Action',
    'user_agent' => 'User Agent',
    'system' => 'System',
    'delete_all' => 'Delete all records',
    'empty_logs' => 'Empty logs',
    'confirm_empty_logs_msg' => 'Do you really want to empty logs?',
    'clear_old_data' => 'Clear old Audit Logs',
    'clear_old_data_helper' => 'Automatically delete audit logs that are older than the selected period to keep your database clean and optimized.',
    'cronjob_warning' => 'To use this feature, you need to set up a cron job by following this link: <a href=":link" target="_blank">:link</a>.',
    'customer' => 'customer',
    'registered' => 'registered',
    'to_the_admin_panel' => 'to the admin panel',
    'to_the_customer_portal' => 'to the customer portal',
    'logged_in' => 'logged in',
    'from_the_admin_panel' => 'from the admin panel',
    'from_the_customer_portal' => 'from the customer portal',
    'logged_out' => 'logged out',
    'admin' => 'admin',
    'register_an_account' => 'an account',
    'activity_has_been_deleted' => 'Activity has been deleted',
];

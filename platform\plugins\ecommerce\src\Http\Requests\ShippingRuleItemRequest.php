<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Http\Requests;

use Bo<PERSON><PERSON>\Base\Facades\BaseHelper;
use <PERSON><PERSON>ble\Ecommerce\Enums\ShippingRuleTypeEnum;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Bo<PERSON>ble\Ecommerce\Models\ShippingRule;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class ShippingRuleItemRequest extends Request
{
    public function rules(): array
    {
        return [
            'shipping_rule_id' => [
                'required',
                Rule::exists(ShippingRule::class, 'id')->where(function ($query) {
                    return $query->whereIn('type', ShippingRuleTypeEnum::keysAllowRuleItems());
                }),
            ],
            'country' => ['required'],
            'state' => [
                'sometimes',
                Rule::requiredIf(function () {
                    return ShippingRule::query()
                        ->where([
                            'id' => $this->input('shipping_rule_id'),
                            'type' => ShippingRuleTypeEnum::BASED_ON_LOCATION,
                        ])
                        ->exists();
                }),
                Rule::exists('states', 'id'),
            ],
            'city' => ['nullable', 'required_without:state'] + (EcommerceHelper::useCityFieldAsTextField() ? [] : ['exists:cities,id']),
            'zip_code' => [
                'nullable',
                ...BaseHelper::getZipcodeValidationRule(true),
                Rule::requiredIf(function () {
                    return ShippingRule::query()
                        ->where([
                            'id' => $this->input('shipping_rule_id'),
                            'type' => ShippingRuleTypeEnum::BASED_ON_ZIPCODE,
                        ])
                        ->exists();
                }),
            ],
            'adjustment_price' => ['nullable', 'numeric', 'min:-100000000000', 'max:100000000000'],
            'is_enabled' => Rule::in(['0', '1']),
        ];
    }
}

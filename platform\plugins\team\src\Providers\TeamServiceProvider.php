<?php

namespace Botble\Team\Providers;

use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use <PERSON><PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use <PERSON><PERSON>ble\LanguageAdvanced\Supports\LanguageAdvancedManager;
use <PERSON><PERSON><PERSON>\SeoHelper\Facades\SeoHelper;
use Bo<PERSON>ble\SeoHelper\SeoOpenGraph;
use Bo<PERSON>ble\Slug\Facades\SlugHelper;
use Botble\Slug\Models\Slug;
use Botble\Team\Models\Team;
use Botble\Theme\Facades\Theme;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;

class TeamServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/team')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes();

        if (defined('LANGUAGE_MODULE_SCREEN_NAME') && defined('LANGUAGE_ADVANCED_MODULE_SCREEN_NAME')) {
            LanguageAdvancedManager::registerModule(Team::class, [
                'name',
                'title',
                'location',
                'content',
                'phone',
                'address',
            ]);
        }

        SlugHelper::registering(function (): void {
            SlugHelper::registerModule(Team::class, fn () => trans('plugins/team::team.teams'));
            SlugHelper::setPrefix(Team::class, 'teams', true);
        });

        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-team',
                    'priority' => 5,
                    'name' => 'plugins/team::team.name',
                    'icon' => 'ti ti-users',
                    'route' => 'team.index',
                ]);
        });

        $this->app->booted(function (): void {
            add_filter(BASE_FILTER_PUBLIC_SINGLE_DATA, function (Slug|array $slug): Slug|array {
                if (! $slug instanceof Slug || $slug->reference_type != Team::class) {
                    return $slug;
                }

                $condition = [
                    'id' => $slug->reference_id,
                    'status' => BaseStatusEnum::PUBLISHED,
                ];

                if (Auth::guard()->check() && request()->input('preview')) {
                    Arr::forget($condition, 'status');
                }

                $team = Team::query()
                    ->where($condition)
                    ->with(['slugable'])
                    ->firstOrFail();

                SeoHelper::setTitle($team->name)
                    ->setDescription($team->description);

                SeoHelper::setSeoOpenGraph(
                    (new SeoOpenGraph())
                        ->setDescription($team->description)
                        ->setUrl($team->url)
                        ->setTitle($team->name)
                        ->setType('article')
                );

                Theme::breadcrumb()->add($team->name);

                return [
                    'view' => 'teams.team',
                    'default_view' => 'plugins/team::themes.team',
                    'data' => compact('team'),
                    'slug' => $team->slug,
                ];
            }, 2);
        });
    }
}

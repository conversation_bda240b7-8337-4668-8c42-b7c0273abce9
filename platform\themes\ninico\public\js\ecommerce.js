(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){for(var a=0;a<t.length;a++){var o=t[a];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}function a(e,t,a){return(t=r(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function r(t){var a=function(t,a){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,a||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(t)}(t,"string");return"symbol"==e(a)?a:a+""}var o=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),a(this,"$body",$(document.body)),a(this,"$productsFilter",this.$body.find(".bb-product-form-filter"))},r=[{key:"init",value:function(){var e=this;this.$body.on("click",".add-to-cart:not(.cart-form button[type=submit])",(function(t){e.addToCart(t)})).on("click",".remove-cart-item",(function(t){e.removeFromCart(t)})).on("click",".btn-apply-coupon-code",(function(t){e.applyCouponCode(t)})).on("click",".btn-remove-coupon-code",(function(t){e.removeCouponCode(t)})).on("click",".product-quantity span",(function(t){e.changeCartQuantity(t)})).on("keyup",".product-quantity input",(function(t){e.onChangeQuantityInput(t)})).on("click",".add-to-compare",(function(t){e.addToCompare(t)})).on("click",".js-sale-popup-quick-view-button",(function(t){e.quickView(t)})).on("click",".tpproduct .quickview",(function(t){e.quickView(t)})).on("click",".tpproduct .button-quick-shop",(function(t){e.quickShop(t)})).on("click",".remove-compare-item",(function(t){e.removeFromCompare(t)})).on("click",".add-to-wishlist",(function(t){e.addToWishlist(t)})).on("click",".remove-wishlist-item",(function(t){e.removeFromWishlist(t)})).on("click",".product-area .basic-pagination ul li a",(function(t){e.handleProductsPagination(t)})).on("change",'.product-area .tp-shop-selector select[name="sort-by"]',(function(t){e.handleProductsSorting(t)})).on("change",'.product-area .tp-shop-selector select[name="per-page"]',(function(t){e.handleProductsPerPage(t)})).on("click",".product-area .product-filter-nav button",(function(t){e.handleProductsLayout(t)})).on("change",".bb-product-form-filter select, input",(function(t){$(t.currentTarget).closest("#quick-shop-popup").length||e.$body.find(".bb-product-form-filter").trigger("submit")})).on("click",".product-filter-button",(function(){e.$body.find(".product-filter-mobile").addClass("active")})).on("click",".product-filter-mobile .backdrop, .close-product-filter-mobile",(function(){e.$body.find(".product-filter-mobile").removeClass("active")})).on("click","form.cart-form button[type=submit]",(function(t){e.addProductToCart(t)})).on("click",".tpproduct-details__reviewers",(function(){e.$body.find(".tpproduct-details__nav #reviews-tab").trigger("click");var t=$(".tpproduct-details__navtab");t.length&&$("html, body").animate({scrollTop:t.offset().top-100})})).on("click",".product-sidebar__list .f-right",(function(e){e.preventDefault(),$(e.currentTarget).closest(".category-filter").find(".product-sidebar__list").slideToggle()})),this.priceFilter(),this.productGallery($(".product-gallery")),this.quickSearchProducts();var t=this;window.onBeforeChangeSwatches=function(e,t){var a=t.closest(".tpproduct-details__content"),r=a.find(".cart-form");a.find(".error-message").hide(),a.find(".success-message").hide(),a.find(".number-items-available").html("").hide();var o=r.find("button[type=submit]");e&&o.prop("disabled",!0)},window.onChangeSwatchesSuccess=function(e,a){var r=a.closest(".tpproduct-details__content"),o=r.find(".cart-form"),n=$(".footer-cart-form");if(e){var i=o.find("button[type=submit]");if(e.error)i.prop("disabled",!0),r.find(".number-items-available").html("<span class='text-danger'>(".concat(e.message,")</span>")).show(),o.find(".hidden-product-id").val(""),n.find(".hidden-product-id").val("");else{var c=e.data,l=r.find(".tpproduct-details__price"),s=l.find(".product-price-sale"),d=l.find(".product-price-original");c.sale_price!==c.price?d.removeClass("d-none"):d.addClass("d-none"),s.text(c.display_sale_price),d.text(c.display_price),c.sku?(r.find(".meta-sku .meta-value").text(c.sku),r.find(".meta-sku").removeClass("d-none")):r.find(".meta-sku").addClass("d-none"),o.find(".hidden-product-id").val(c.id),n.find(".hidden-product-id").val(c.id),i.prop("disabled",!1),c.error_message?(i.prop("disabled",!0),r.find(".number-items-available").html("<span class='text-danger'>(".concat(c.error_message,")</span>")).show()):c.success_message?r.find(".number-items-available").html(e.data.stock_status_html).show():r.find(".number-items-available").html("").hide(),r.find(".tpproduct-details__stock").html(c.stock_status_html);var u=c.unavailable_attribute_ids||[];r.find(".attribute-swatch-item").removeClass("disabled"),r.find(".product-filter-item option").prop("disabled",!1),u&&u.length&&u.map((function(e){var t=r.find('.attribute-swatch-item[data-id="'.concat(e,'"]'));t.length?(t.addClass("disabled"),t.find("input").prop("checked",!1)):(t=r.find('.product-filter-item option[data-id="'.concat(e,'"]'))).length&&t.prop("disabled","disabled").prop("selected",!1)}));var p=r.closest(".product-area").find(".product-gallery"),f="";c.image_with_sizes.origin.forEach((function(e){f+="<a href='".concat(e,"'>\n                        <img title='").concat(c.name,"' title='").concat(c.name,"' src='").concat(siteConfig.img_placeholder?siteConfig.img_placeholder:e,"' data-lazy='").concat(e,"'>\n                    </a>")})),p.find(".product-gallery__wrapper").slick("unslick").html(f);var m="";c.image_with_sizes.thumb.forEach((function(e){m+="<img alt='".concat(c.name,"' title='").concat(c.name,"' src='").concat(siteConfig.img_placeholder?siteConfig.img_placeholder:e,"' data-src='").concat(e,"' data-lazy='").concat(e,"'>")})),p.find(".product-thumbnails").slick("unslick").html(m),t.productGallery(p),setTimeout((function(){var e=$(".product-gallery__wrapper");e.length&&!e.width()&&t.productGallery(p)}),1500)}}}}},{key:"productGallery",value:function(e){if(e.length){var t=e.find(".product-gallery__wrapper"),a=e.find(".product-thumbnails");t.length&&(t.hasClass("slick-initialized")&&t.slick("unslick"),t.slick({rtl:Theme.isRtl(),slidesToShow:1,slidesToScroll:1,infinite:!1,dots:!1,arrows:!1,lazyLoad:"ondemand"})),a.length&&(a.hasClass("slick-initialized")&&a.slick("unslick"),a.slick({rtl:Theme.isRtl(),slidesToShow:6,slidesToScroll:1,infinite:!1,focusOnSelect:!0,asNavFor:t,vertical:1===a.data("vertical"),nextArrow:'<button class="slick-next slick-arrow"><i class="fas fa-chevron-down"></i></button>',prevArrow:'<button class="slick-prev slick-arrow"><i class="fas fa-chevron-up"></i></button>',responsive:[{breakpoint:768,settings:{slidesToShow:4,vertical:!1}},{breakpoint:480,settings:{slidesToShow:3,vertical:!1}}]})),this.lightGallery(e)}}},{key:"quickSearchProducts",value:function(){var e=$(".form--quick-search");$("body").on("click",(function(e){$(e.target).closest(".form--quick-search").length||$(".panel--search-result").removeClass("active")}));var t=null;function a(a){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=a.find(".panel--search-result"),n=a.find(".input-search-product").val();if(n){e.find(".input-search-product").val(n);var i=a.find("button[type=submit]");t=$.ajax({type:"GET",url:r||a.data("url"),dataType:"json",data:r?[]:a.serialize(),beforeSend:function(){i.addClass("loading"),null!==t&&t.abort()},success:function(e){var t=e.error,a=e.data;if(t)o.html("").removeClass("active");else if(r){var n=$("<div>".concat(a,"</div>"));o.find(".panel__content").find(".loadmore-container").remove(),o.find(".panel__content").append(n.find(".panel__content p-3").contents())}else o.html(a).addClass("active")},complete:function(){i.removeClass("loading")}})}else o.html("").removeClass("active")}e.on("keyup",".input-search-product",(function(){a($(this).closest("form"))})),e.on("change",".product-category-select",(function(){a($(this).closest("form"))})),e.on("click",".loadmore",(function(e){e.preventDefault();var t=$(this).closest("form");$(this).addClass("loading"),a(t,$(this).attr("href"))}))}},{key:"addToCart",value:function(e){var t=this;e.preventDefault();var a=$(e.currentTarget);$.ajax({url:a.data("url"),method:"POST",data:{id:a.data("id")},dataType:"json",beforeSend:function(){a.addClass("loading")},success:function(e){var a=e.error,r=e.message;a?Theme.showError(r):(t.loadAjaxCart(),t.$body.find(".tp-cart-toggle").trigger("click"))},error:function(e){return Theme.handleError(e)},complete:function(){a.removeClass("loading")}})}},{key:"addProductToCart",value:function(e){var t=this;e.preventDefault();var a=$(e.currentTarget),r=a.closest("form.cart-form"),o=r.serializeArray();o.push({name:"checkout",value:"checkout"===a.prop("name")?1:0}),$.ajax({type:"POST",url:r.prop("action"),data:$.param(o),beforeSend:function(){a.addClass("button-loading")},success:function(e){var a=e.error,r=e.message,o=e.data;if(a)return Theme.showError(r),void(void 0!==(null==o?void 0:o.next_url)&&setTimeout((function(){window.location.href=o.next_url}),500));void 0===(null==o?void 0:o.next_url)?(t.$body.find(".tp-cart-toggle").trigger("click"),t.loadAjaxCart()):window.location.href=o.next_url},error:function(e){Theme.handleError(e)},complete:function(){a.removeClass("button-loading")}})}},{key:"addToCompare",value:function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),method:"POST",beforeSend:function(){t.addClass("loading")},success:function(e){var t=e.error,a=e.data,r=e.message;t?Theme.showError(r):(Theme.showSuccess(r),$(".header-cart .tp-product-compare-count").text(a.count))},error:function(e){Theme.handleError(e)},complete:function(){t.removeClass("loading")}})}},{key:"removeFromCompare",value:function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),method:"POST",data:{_method:"DELETE"},success:function(e){var t=e.error,a=e.data,r=e.message;t?Theme.showError(r):(Theme.showSuccess(r),$(".header-cart .tp-product-compare-count").text(a.count),$(".compare-area").load(window.location.href+" .compare-area > *"))},error:function(e){Theme.handleError(e)}})}},{key:"removeFromCart",value:function(e){var t=this;e.preventDefault();var a=$(e.currentTarget);$.ajax({url:a.data("url"),method:"GET",beforeSend:function(){a.addClass("loading")},success:function(e){var a;if(e.error)Theme.showError(e.message);else{var r=$(".cart-area");r.length&&null!==(a=window.siteConfig)&&void 0!==a&&a.cartUrl&&r.load(window.siteConfig.cartUrl+" .cart-area > *"),t.loadAjaxCart()}},error:function(e){Theme.showError(e.message)},complete:function(){a.removeClass("loading")}})}},{key:"addToWishlist",value:function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),method:"POST",beforeSend:function(){t.addClass("loading")},success:function(e){var a=e.error,r=e.message,o=e.data;a?Theme.showError(r):(Theme.showSuccess(r),$(".header-cart .tp-product-wishlist-count").text(o.count),o.added?t.find("i").removeClass("fal").addClass("fas"):t.find("i").removeClass("fas").addClass("fal"))},error:function(e){Theme.handleError(e)},complete:function(){t.removeClass("loading")}})}},{key:"removeFromWishlist",value:function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),method:"POST",data:{_method:"DELETE"},success:function(e){e.error?Theme.showError(e.message):(Theme.showSuccess(e.message),$(".header-cart .tp-product-wishlist-count").text(e.data.count),$(".wishlist-area").load(window.location.href+" .wishlist-area > *"))},error:function(e){Theme.handleError(e)}})}},{key:"loadAjaxCart",value:function(){var e,t=this;null!==(e=window.siteConfig)&&void 0!==e&&e.ajaxCart&&$.ajax({url:window.siteConfig.ajaxCart,method:"GET",success:function(e){var a=e.data;e.error||(t.$body.find(".tpcartinfo .tpcart__product").html(a.html),t.$body.find(".header-cart .tp-product-count").text(a.count))}})}},{key:"applyCouponCode",value:function(e){e.preventDefault();var t=$(e.currentTarget),a=t.closest(".coupon").find("#coupon_code").val();$.ajax({url:t.data("url"),type:"POST",data:{coupon_code:a},beforeSend:function(){t.prop("disabled",!0).addClass("button-loading")},success:function(e){e.error?Theme.showError(e.message):$(".cart-area").load(window.location.href+"?applied_coupon=1 .cart-area > *",(function(){t.prop("disabled",!1).removeClass("button-loading"),Theme.showSuccess(e.message)}))},error:function(e){Theme.handleError(e)},complete:function(e){var a;(200!==e.status||null!=e&&null!==(a=e.responseJSON)&&void 0!==a&&a.error)&&t.prop("disabled",!1).removeClass("button-loading")}})}},{key:"removeCouponCode",value:function(e){e.preventDefault();var t=$(e.currentTarget),a=t.text();t.text(t.data("loading-text")),$.ajax({url:t.data("url"),type:"POST",success:function(e){e.error?Theme.showError(e.message):$(".cart-area").load(window.location.href+" .cart-area > *",(function(){t.text(a)}))},error:function(e){Theme.handleError(e)},complete:function(e){var r;(200!==e.status||null!=e&&null!==(r=e.responseJSON)&&void 0!==r&&r.error)&&t.text(a)}})}},{key:"changeCartQuantity",value:function(e){var t=$(e.target),a=t.parent().find("input"),r=parseInt(a.attr("step"),10),o=parseInt(a.attr("min"),10),n=parseInt(a.attr("max"),10),i=parseInt(a.val(),10);t.hasClass("cart-minus")&&i>o&&(a.val(i-r),a.trigger("change")),t.hasClass("cart-plus")&&i<n&&(a.val(i+r),a.trigger("change")),this.updateCart(e)}},{key:"onChangeQuantityInput",value:function(e){var t=$(e.target),a=parseInt(t.attr("min"),10),r=parseInt(t.attr("max"),10),o=parseInt(t.val(),10);o<a&&t.val(a),o>r&&t.val(r),this.updateCart(e)}},{key:"updateCart",value:function(e){var t=this;e.preventDefault();var a=this.$body.find(".cart-form");a.length&&$.ajax({type:"POST",cache:!1,url:a.prop("action"),data:new FormData(a[0]),contentType:!1,processData:!1,success:function(e){var a=e.error,r=e.message;a?Theme.showError(r):($(".cart-area").load(window.siteConfig.cartUrl+" .cart-area > *"),t.loadAjaxCart(),Theme.showSuccess(r))},error:function(e){Theme.handleError(e)}})}},{key:"handleProductsPagination",value:function(e){e.preventDefault();var t=new URL($(e.currentTarget).attr("href")).searchParams.get("page");this.$body.find(".bb-product-form-filter").find('input[name="page"]').val(t).trigger("change")}},{key:"handleProductsSorting",value:function(e){var t=$(e.currentTarget);this.$body.find(".bb-product-form-filter").find('input[name="sort-by"]').val(t.val()).trigger("change")}},{key:"handleProductsPerPage",value:function(e){var t=$(e.currentTarget);this.$body.find(".bb-product-form-filter").find('input[name="per-page"]').val(t.val()).trigger("change")}},{key:"handleProductsLayout",value:function(e){var t=$(e.currentTarget);t.addClass("active"),t.siblings().removeClass("active"),this.$body.find(".bb-product-form-filter").find('input[name="layout"]').val(t.data("type")).trigger("change")}},{key:"priceFilter",value:function(){var e=$(document).find("#slider-range");if(e.length){var t=e.data("min"),a=e.data("max"),r=$(document).find(".price-filter");e.slider({range:!0,min:t,max:a,values:[r.find('input[name="min_price"]').val(),r.find('input[name="max_price"]').val()],slide:function(e,t){r.find("#amount").text("".concat(t.values[0].format_price()," - ").concat(t.values[1].format_price()))},change:function(e,t){r.find('input[name="min_price"]').val(t.values[0]),r.find('input[name="max_price"]').val(t.values[1]).trigger("change")}}),r.find("#amount").text("".concat(e.slider("values",0).format_price()," - ").concat(e.slider("values",1).format_price()))}}},{key:"lightGallery",value:function(e){e.data("lightGallery")&&e.data("lightGallery").destroy(!0),e.lightGallery({selector:"a",thumbnail:!0,share:!1,fullScreen:!1,autoplay:!1,autoplayControls:!1,actualSize:!1})}},{key:"quickView",value:function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),type:"GET",beforeSend:function(){t.addClass("loading")},success:function(e){var t=e.data;$("#quick-view-popup").html(t),$.magnificPopup.open({items:{src:"#quick-view-popup"},type:"inline"}),$(".thumbnails .images").slick({slidesToShow:1,slidesToScroll:1,dots:!0,arrows:!0,adaptiveHeight:!1,rtl:Theme.isRtl()})},error:function(e){Theme.handleError(e)},complete:function(){t.removeClass("loading")}})}},{key:"quickShop",value:function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),type:"GET",beforeSend:function(){t.addClass("loading")},success:function(e){var t=e.data;$("#quick-shop-popup").html(t),$.magnificPopup.open({items:{src:"#quick-shop-popup"},type:"inline"})},error:function(e){Theme.handleError(e)},complete:function(){t.removeClass("loading")}})}}],r&&t(e.prototype,r),o&&t(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,o}();$((function(){var e=new o;e.init(),setTimeout((function(){var t=$(".product-gallery__wrapper");t.length&&!t.width()&&e.productGallery($(".product-gallery"))}),1500),$.each($('.product-sidebar__list .category-filter input[type="checkbox"]:checked'),(function(){$(this).closest(".product-sidebar__list").show()}));var t=$(document).find(".product-sidebar__product-item");function a(){if($(".nav-tabs button#reviews-tab").length){var e=$(".nav-tabs button#reviews-tab"),t=$(".product-review-container");e.length&&t.length&&(e.tab("show"),$("html, body").animate({scrollTop:t.offset().top-120}))}}document.addEventListener("ecommerce.product-filter.before",(function(){t.find(".loading-spinner").removeClass("d-none")})),document.addEventListener("ecommerce.product-filter.success",(function(e){$(".product-filter-content .product-item-count span").html(e.detail.data.message),t.find(".loading-spinner").addClass("d-none")})),$(document).on("click",'[data-bb-toggle="scroll-to-review"]',(function(e){e.preventDefault(),a()})),-1!==window.location.href.indexOf("#reviews")&&a()}))})();
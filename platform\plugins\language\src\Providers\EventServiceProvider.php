<?php

namespace Bo<PERSON>ble\Language\Providers;

use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Installer\Events\InstallerFinished;
use Bo<PERSON>ble\Language\Listeners\ActivatedPluginListener;
use Botble\Language\Listeners\AddHrefLangListener;
use Botble\Language\Listeners\CopyThemeOptions;
use Botble\Language\Listeners\CopyThemeWidgets;
use Botble\Language\Listeners\CreatedContentListener;
use Botble\Language\Listeners\CreateSelectedLanguageWhenInstallationFinished;
use Botble\Language\Listeners\DeletedContentListener;
use Botble\Language\Listeners\ThemeRemoveListener;
use Botble\Language\Listeners\UpdatedContentListener;
use Botble\PluginManagement\Events\ActivatedPluginEvent;
use Botble\Theme\Events\RenderingSingleEvent;
use Bo<PERSON>ble\Theme\Events\ThemeRemoveEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UpdatedContentEvent::class => [
            UpdatedContentListener::class,
        ],
        CreatedContentEvent::class => [
            CreatedContentListener::class,
            CopyThemeOptions::class,
            CopyThemeWidgets::class,
        ],
        DeletedContentEvent::class => [
            DeletedContentListener::class,
        ],
        ThemeRemoveEvent::class => [
            ThemeRemoveListener::class,
        ],
        ActivatedPluginEvent::class => [
            ActivatedPluginListener::class,
        ],
        RenderingSingleEvent::class => [
            AddHrefLangListener::class,
        ],
        InstallerFinished::class => [
            CreateSelectedLanguageWhenInstallationFinished::class,
        ],
    ];
}

@use '../utils' as *;

.tpshopitem {
    height: 220px;
    width: 220px;
    object-fit: cover;

    & img {
        width: 100%;
        border-radius: 6px;
    }

    @media #{$xs} {
        text-align: center;
    }
}

.whiteproduct {
    background-color: var(--tp-common-white);

    &__thumb {
        & img {
            max-width: 100%;
        }
    }

    &__content {
        padding: 0 40px 40px 40px;
    }

    &__title {
        color: var(--tp-text-secondary);
        font-size: 15px;
        margin-bottom: 2px;

        & a {
            &:hover {
                color: var(--tp-text-primary);
            }
        }
    }

    &__text {
        & span {
            font-weight: 600;
            font-size: 16px;
        }
    }

    &__rating {
        & i {
            color: var(--tp-text-primary);
            font-size: 14px;
        }

        & span {
            font-size: 14px;
        }
    }
}

.tpproductarrow {
    position: relative;
    z-index: 2;
    justify-content: flex-end;
    @media #{$xs} {
        justify-content: start;
        margin-bottom: 15px;
    }

    &__prv {
        font-size: 14px;
        font-weight: 600;
        background-color: #fff;
        padding: 6px 14px;
        line-height: 1;
        margin-inline-end: 10px;
        border-radius: 6px;
        @include transition(0.3s);

        &:hover {
            background-color: var(--tp-text-primary);
            color: var(--tp-common-white);

            & i {
                color: var(--tp-common-white);
            }
        }

        & i {
            margin-inline-end: 6px;
        }
    }

    &__nxt {
        font-size: 14px;
        font-weight: 600;
        background-color: #fff;
        padding: 6px 14px;
        line-height: 1;
        border-radius: 6px;
        @include transition(0.3s);

        &:hover {
            background-color: var(--tp-text-primary);
            color: var(--tp-common-white);

            & i {
                color: var(--tp-common-white);
            }
        }

        & i {
            margin-inline-start: 6px;
        }
    }
}

.tpsidebar {
    & li {
        list-style: none;
    }

    &__form-check {
        & .form-check-input {
            margin-top: 0;
            box-shadow: none;
        }

        & .form-check-input[type='checkbox'] {
            border-radius: 0;
        }

        & label {
            font-size: 14px;
            color: var(--tp-text-secondary);
            margin-inline-start: 10px;
        }

        & span {
            margin-inline-start: auto;
        }
    }
}

.filter-product-title {
    font-size: 20px;
    margin-bottom: 10px;
    font-weight: 700;
    @media #{$xs} {
        font-size: 18px;
    }

    & a {
        &:hover {
            color: var(--tp-text-primary);
        }
    }
}

.tpproduct__amount {
    font-size: 16px;
    font-weight: 600;

    & del {
        font-weight: 500;
        color: #747691;
    }
}

.tpproduct__action {
    & a {
        font-size: 16px;
        color: var(--tp-text-body);
        height: 50px;
        width: 50px;
        text-align: center;
        line-height: 50px;
        background-color: var(--tp-common-white);
        border-radius: 6px;
        display: inline-block;
        box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);

        &:hover {
            background-color: var(--tp-text-primary);
            color: var(--tp-common-white);
        }
    }
}

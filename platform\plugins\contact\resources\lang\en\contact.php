<?php

return [
    'name' => 'Contacts',
    'contact_form' => 'Contact form',
    'menu' => 'Contact',
    'edit' => 'View contact',
    'tables' => [
        'phone' => 'Phone',
        'email' => 'Email',
        'full_name' => 'Full Name',
        'time' => 'Time',
        'address' => 'Address',
        'subject' => 'Subject',
        'content' => 'Content',
    ],
    'contact_information' => 'Contact information',
    'replies' => 'Replies',
    'email' => [
        'header' => 'Email',
        'title' => 'New contact from your site',
    ],
    'form' => [
        'name' => [
            'required' => 'Name is required',
        ],
        'email' => [
            'required' => 'Email is required',
            'email' => 'The email address is not valid',
        ],
        'content' => [
            'required' => 'Content is required',
        ],
    ],
    'contact_sent_from' => 'This contact information sent from',
    'sender' => 'Sender',
    'sender_email' => 'Email',
    'sender_address' => 'Address',
    'sender_phone' => 'Phone',
    'message_content' => 'Message content',
    'sent_from' => 'Email sent from',
    'form_name' => 'Name',
    'form_email' => 'Email',
    'form_address' => 'Address',
    'form_subject' => 'Subject',
    'form_phone' => 'Phone',
    'form_message' => 'Message',
    'form_content' => 'Content',
    'required_field' => 'The field with (<span style="color: red">*</span>) is required.',
    'send_btn' => 'Send message',
    'new_msg_notice' => 'You have <span class="bold">:count</span> New Messages',
    'view_all' => 'View all',
    'statuses' => [
        'read' => 'Read',
        'unread' => 'Unread',
    ],
    'phone' => 'Phone',
    'address' => 'Address',
    'message' => 'Message',
    'settings' => [
        'email' => [
            'title' => 'Contact',
            'description' => 'Contact email configuration',
            'templates' => [
                'notice_title' => 'Send notice to administrator',
                'notice_description' => 'Email template for notifying administrator upon receiving a new contact submission',
                'subject' => 'Message sent via contact form at {{ site_title }}',
                'contact_name' => 'Contact name',
                'contact_subject' => 'Contact subject',
                'contact_email' => 'Contact email',
                'contact_phone' => 'Contact phone',
                'contact_address' => 'Contact address',
                'contact_content' => 'Contact content',
                'contact_custom_fields' => 'Custom fields',
                'sender_confirmation_title' => 'Send confirmation to sender',
                'sender_confirmation_description' => 'Email template for confirming the sender that the message has been sent successfully',
                'sender_confirmation_subject' => 'Thank You for Contacting Us!',
            ],
        ],
        'title' => 'Contact',
        'description' => 'Settings for contact plugin',
        'blacklist_keywords' => 'Blacklisted keywords',
        'blacklist_keywords_placeholder' => 'keywords...',
        'blacklist_keywords_helper' => 'Add contact requests to blacklist if those keywords are found in the content field (separated by comma).',
        'enable_math_captcha_in_contact_form' => 'Enable Math CAPTCHA for contact forms',
        'receiver_emails' => 'Receiver emails',
        'receiver_emails_placeholder' => 'You can enter more than 1 email address (separated by comma)',
        'receiver_emails_helper' => 'Emails from contact forms will be sent to these addresses. To send emails to admin email addresses, leave this field blank.',
        'show_terms_checkbox' => 'Show Terms and Privacy Policy checkbox',
        'show_terms_checkbox_helper' => 'Enable to show the "I agree to the Terms and Privacy Policy" checkbox in the contact form.',
    ],
    'no_reply' => 'No reply yet!',
    'reply' => 'Reply',
    'send' => 'Send',
    'shortcode_name' => 'Contact form',
    'shortcode_description' => 'Add a contact form',
    'shortcode_content_description' => 'Add shortcode [contact-form][/contact-form] to editor',
    'message_sent_success' => 'Message sent successfully!',
    'dropdown_show_label' => 'Show contacts',
    'display_fields' => 'Display fields',
    'mandatory_fields' => 'Mandatory fields',
    'mandatory_fields_helper_text' => 'If left blank, these fields will be validated by default configuration. Name and Message are always mandatory.',
    'custom_field' => [
        'name' => 'Custom Fields',
        'create' => 'Create Custom Field',
        'type' => 'Type',
        'required' => 'Required',
        'placeholder' => 'Placeholder',
        'order' => 'Order',
        'options' => 'Options',
        'option' => [
            'label' => 'Label',
            'value' => 'Value',
            'add' => 'Add new option',
        ],
        'enums' => [
            'types' => [
                'text' => 'Text',
                'number' => 'Number',
                'dropdown' => 'Dropdown',
                'checkbox' => 'Checkbox',
                'radio' => 'Radio',
                'textarea' => 'Textarea',
                'date' => 'Date',
                'datetime' => 'Date time',
                'time' => 'Time',
            ],
        ],
    ],
];

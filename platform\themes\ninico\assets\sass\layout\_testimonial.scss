@use '../utils' as *;

.tptesti {
    background-color: #fcf6f4;
    padding: 40px 64px;
    border-radius: 6px;
    @media #{$xl,$md} {
        padding: 40px 30px;
    }
    @media #{$lg,$xs} {
        padding: 30px 25px;
    }

    &__icon {
        height: 50px;
        width: 50px;
        line-height: 50px;
        display: inline-block;
        background: #fff;
        text-align: center;
        border-radius: 50%;
    }

    &__content {
        & p {
            font-size: 20px;
            color: var(--tp-text-body);
            line-height: 1.6;
            @media #{$lg} {
                font-size: 17px;
            }
            @media #{$xs} {
                font-size: 16px;
            }
        }
    }

    &__avata {
        &-content {
            &-title {
                font-weight: 600;
                font-size: 18px;
                margin-bottom: 5px;
                @media #{$sm} {
                    font-size: 14px;
                }
            }

            & p {
                margin-bottom: 0;
                font-size: 14px;
                color: var(--tp-text-secondary);
            }
        }
    }
}

.tptestiarrow {
    @media #{$xs} {
        justify-content: start !important;
        margin-bottom: 15px;
    }
}

.tpplatiarrow,
.tptestiarrow {
    &__prv,
    &__nxt {
        font-size: 16px;
        font-weight: 600;
        color: #c0c0c0;
        @include transition(0.3s);

        &:hover {
            color: var(--tp-text-body);
        }
    }

    &__nxt {
        margin-inline-start: 20px;

        & i {
            margin-inline-start: 12px;
        }
    }

    &__prv {
        & i {
            margin-inline-end: 12px;
        }
    }
}

.tp-white-testimonial {
    & .tptesti {
        background-color: var(--tp-common-white);
    }

    & .tptesti__icon {
        background-color: var(--tp-text-primary);
    }

    & .tptesti__content {
        & p {
            color: var(--tp-text-secondary);
        }
    }

    & .tptestiarrow__prv,
    & .tptestiarrow__nxt {
        font-size: 14px;
        font-weight: 600;
        color: var(--tp-text-body);
        background-color: var(--tp-common-white);
        padding: 6px 14px;
        line-height: 1;
        margin-inline-end: 10px;
        border-radius: 6px;
        transition: all 0.3s ease-out 0s;

        &:hover {
            color: var(--tp-common-white);
            background-color: var(--tp-text-primary);
        }
    }

    & .tptestiarrow__nxt {
        margin-inline-end: 0;
        margin-inline-start: 0;
    }
}

.tpteam {
    &__thumb {
        position: relative;

        &::before {
            transition: all 0.4s ease-out 0s;
            position: absolute;
            content: '';
            width: 100%;
            height: 100%;
            background-image: linear-gradient(0deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
            background-image: -moz-linear-gradient(90deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
            background-image: -webkit-linear-gradient(90deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
            background-image: -ms-linear-gradient(90deg, rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0) 100%);
            inset-inline-start: 0;
            inset-inline-end: 0;
            bottom: 0;
            z-index: 1;
            opacity: 0.6;
            border-radius: 6px;
        }

        & img {
            border-radius: 6px;
            width: 100%;
        }
    }

    &__position {
        font-size: 14px;
        color: var(--tp-common-white);
        margin-bottom: 7px;
    }

    &__title {
        font-size: 24px;
        font-weight: 600;
        color: var(--tp-common-white);
        margin-bottom: 0;
        @media #{$sm} {
            font-size: 18px;
        }
    }

    &__content {
        position: absolute;
        bottom: 35px;
        z-index: 9;
        inset-inline-start: 38px;
    }

    &__btn {
        border: 1px solid var(--tp-border-1);
        font-size: 18px;
        font-weight: 600;
        color: var(--tp-text-body);
        padding: 16px 33px;
        border-radius: 6px;
        display: inline-block;
        @include transition(0.3s);
        @media #{$xl,$lg,$sm} {
            padding: 10px 33px;
            font-size: 16px;
        }

        &:hover {
            background-color: var(--tp-text-primary);
            border: 1px solid var(--tp-text-primary);
            color: var(--tp-common-white);
        }
    }
}

.tpfeature {
    &__inner-thumb {
        & img {
            max-width: 100%;
        }
    }
}

body[dir='rtl'] {
    text-align: right;

    .show-cart-link {
        i {
            float: left;
        }
    }

    .float-end {
        float: left !important;
    }

    .float-start {
        float: right !important;
    }

    .text-start {
        text-align: right !important;
    }

    .text-end {
        text-align: left !important;
    }

    .list-group {
        padding-right: 0;
    }

    input[type='checkbox'] {
        &:before {
            left: 0;
            right: 0;
        }

        &:after {
            left: auto;
            right: -2px;
        }
    }

    .magic-checkbox + label:before,
    .magic-radio + label:before {
        left: auto;
        right: 0;
    }

    .magic-radio + label:after {
        left: auto;
        right: 6px;
    }

    .magic-checkbox + label,
    .magic-radio + label {
        padding-left: 0;
        padding-right: 30px;
    }

    @media screen and (min-width: 992px) {
        .left {
            border-left: 1px solid #c8c8c8;
            padding-left: 60px;
        }
        .right {
            padding-right: 50px;
        }
    }

    .price-text,
    .total-text {
        float: left;
    }

    .address-item.is-default .default {
        right: auto;
        left: 15px;
    }

    .select--arrow i {
        right: auto;
        left: 10px;
    }

    .was-validated .form-control:valid, .form-control.is-valid {
        background-position: left calc(0.375em + 0.1875rem) center;
        padding-left: calc(1.5em + 0.75rem);
        padding-right: 0.75rem;
    }

    .select--arrow .form-control {
        padding: 0 15px 0 30px;
    }

    .bb-quick-view-gallery-images {
        .slick-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
    }
}

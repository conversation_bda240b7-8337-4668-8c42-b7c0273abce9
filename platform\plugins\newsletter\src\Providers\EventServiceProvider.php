<?php

namespace Bo<PERSON><PERSON>\Newsletter\Providers;

use Bo<PERSON><PERSON>\Newsletter\Events\SubscribeNewsletterEvent;
use Bo<PERSON>ble\Newsletter\Events\UnsubscribeNewsletterEvent;
use Bo<PERSON>ble\Newsletter\Listeners\AddSubscriberToMailchimpContactListListener;
use Bo<PERSON>ble\Newsletter\Listeners\AddSubscriberToSendGridContactListListener;
use Bo<PERSON>ble\Newsletter\Listeners\RemoveSubscriberToMailchimpContactListListener;
use Botble\Newsletter\Listeners\SendEmailNotificationAboutNewSubscriberListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        SubscribeNewsletterEvent::class => [
            SendEmailNotificationAboutNewSubscriberListener::class,
            AddSubscriberToMailchimpContactListListener::class,
            AddSubscriberToSendGridContactListListener::class,
        ],
        UnsubscribeNewsletterEvent::class => [
            RemoveSubscriberToMailchimpContactListListener::class,
        ],
    ];
}

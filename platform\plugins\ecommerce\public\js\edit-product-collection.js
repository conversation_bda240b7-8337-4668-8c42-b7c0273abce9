$((function(){var t,e,a=!1;(e=$(".wrap-collection-products")).length&&$httpClient.make().withLoading(e).get(e.data("target")).then((function(t){var a=t.data;a.error?Botble.showError(a.message):e.html(a.data)})),$(document).on("click",".list-search-data .selectable-item",(function(t){t.preventDefault();var e=$(t.currentTarget),a=e.closest(".box-search-advance").find("input[type=hidden]"),r=a.val().split(",");if($.each(r,(function(t,e){r[t]=parseInt(e)})),$.inArray(e.data("id"),r)<0){a.val()?a.val("".concat(a.val(),",").concat(e.data("id"))):a.val(e.data("id"));var c=$(document).find("#selected_product_list_template").html().replace(/__name__/gi,e.data("name")).replace(/__id__/gi,e.data("id")).replace(/__url__/gi,e.data("url")).replace(/__image__/gi,e.data("image")).replace(/__attributes__/gi,e.find("a span").text());e.closest(".box-search-advance").find(".list-selected-products").show(),e.closest(".box-search-advance").find(".list-selected-products").append(c)}e.closest(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-search-advanced"]',(function(t){var e=$(t.currentTarget),a=e.closest(".box-search-advance").find(".card");a.show(),a.addClass("active"),0===a.find(".card-body").length&&$httpClient.make().withLoading(a).get(e.data("bb-target")).then((function(t){var e=t.data;e.error?Botble.showError(e.message):a.html(e.data)}))})),$(document).on("keyup",'[data-bb-toggle="product-search-advanced"]',(function(e){e.preventDefault();var r=$(e.currentTarget),c=r.closest(".box-search-advance").find(".card");setTimeout((function(){a&&t.abort(),a=!0,t=$httpClient.make().withLoading(c).get(r.data("bb-target"),{keyword:r.val()}).then((function(t){var e=t.data;e.error?Botble.showError(e.message):c.html(e.data),a=!1})).catch((function(t){"abort"!==data.statusText&&Botble.handleError(t)}))}),500)})),$(document).on("click",".box-search-advance .page-link",(function(t){t.preventDefault();var e=$(t.currentTarget).closest(".box-search-advance").find('[data-bb-toggle="product-search-advanced"]');if(!e.closest(".page-item").hasClass("disabled")&&e.data("bb-target")){var a=e.closest(".box-search-advance").find(".card");$httpClient.make().withLoading(a).get($(t.currentTarget).prop("href"),{keyword:e.val()}).then((function(t){var e=t.data;e.error?Botble.showError(e.message):a.html(e.data)}))}})),$(document).on("click","body",(function(t){var e=$(".box-search-advance");e.is(t.target)||0!==e.has(t.target).length||e.find(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-delete-item"]',(function(t){t.preventDefault();var e=$(t.currentTarget).closest(".box-search-advance").find("input[type=hidden]"),a=e.val().split(",");$.each(a,(function(t,e){e=e.trim(),_.isEmpty(e)||(a[t]=parseInt(e))}));var r=a.indexOf($(t.currentTarget).data("bb-target"));r>-1&&a.splice(r,1),e.val(a.join(",")),$(t.currentTarget).closest(".list-selected-products").find(".list-group-item").length<2&&$(t.currentTarget).closest(".list-selected-products").hide(),$(t.currentTarget).closest(".list-group-item").remove()}))}));
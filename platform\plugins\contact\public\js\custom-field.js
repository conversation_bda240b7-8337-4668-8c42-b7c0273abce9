$((function(){$(".custom-field-options").sortable({cursor:"move"}),$(document).on("change",'.custom-field-form select[name="type"]',(function(o){var t=$(o.currentTarget),e=t.closest("form").find(".custom-field-options-box");"dropdown"===t.val()||"radio"===t.val()?e.show():e.hide()})).on("click",'[data-bb-toggle="add-option"]',(function(o){o.preventDefault();var t=$(o.currentTarget).closest(".card").find(".custom-field-options"),e=t.find("tr").last().clone(),n=t.find("tr").length;e.find('[data-bb-toggle="option-label"]').val("").prop("name","options[".concat(n,"][label]")),e.find('[data-bb-toggle="option-value"]').val("").prop("name","options[".concat(n,"][value]")),t.append(e)})).on("click",'[data-bb-toggle="remove-option"]',(function(o){o.preventDefault(),$(o.currentTarget).closest("tr").remove()}))}));
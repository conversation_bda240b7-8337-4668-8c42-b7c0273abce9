(()=>{var e={27:(e,t,n)=>{"use strict";n.d(t,{K:()=>c});var r=n(4637),i=n(418);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,s(r.key),r)}}function s(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}var c=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return t=e,o=[{key:"initContext",value:function(){jQuery().contextMenu&&($.contextMenu({selector:'.js-context-menu[data-context="file"]',build:function(){return{items:e._fileContextMenu()}}}),$.contextMenu({selector:'.js-context-menu[data-context="folder"]',build:function(){return{items:e._folderContextMenu()}}}))}},{key:"_fileContextMenu",value:function(){var e={preview:{name:"Preview",icon:function(e,t,n,r){return t.html('<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>\n                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>\n                    </svg> '.concat(r.name)),"context-menu-icon-updated"},callback:function(){r.d.handlePreview()}}};i.M.each(i.M.getConfigs().actions_list,(function(t,n){i.M.each(t,(function(t){e[t.action]={name:t.name,icon:function(e,r,o,a){return r.html("".concat(t.icon," ").concat(i.M.trans("actions_list.".concat(n,".").concat(t.action))||a.name)),"context-menu-icon-updated media-action-".concat(t.action)},callback:function(){$('.js-files-action[data-action="'.concat(t.action,'"]')).trigger("click")}}}))}));var t=[];switch(i.M.getRequestParams().view_in){case"all_media":t=["remove_favorite","delete","restore"];break;case"recent":t=["remove_favorite","delete","restore","make_copy"];break;case"favorites":t=["favorite","delete","restore","make_copy"];break;case"trash":e={preview:e.preview,rename:e.rename,download:e.download,delete:e.delete,restore:e.restore}}i.M.each(t,(function(t){e[t]=void 0})),i.M.getSelectedFolder().length>0&&(e.preview=void 0,e.crop=void 0,e.copy_link=void 0,e.copy_indirect_link=void 0,e.share=void 0,e.alt_text=void 0,i.M.hasPermission("folders.create")||(e.make_copy=void 0),i.M.hasPermission("folders.edit")||(e.rename=void 0),i.M.hasPermission("folders.trash")||(e.trash=void 0,e.restore=void 0),i.M.hasPermission("folders.destroy")||(e.delete=void 0),i.M.hasPermission("folders.favorite")||(e.favorite=void 0,e.remove_favorite=void 0));var n=i.M.getSelectedFiles();return n.length>0&&(i.M.hasPermission("files.create")||(e.make_copy=void 0),i.M.hasPermission("files.edit")||(e.rename=void 0),i.M.hasPermission("files.trash")||(e.trash=void 0,e.restore=void 0),i.M.hasPermission("files.destroy")||(e.delete=void 0),i.M.hasPermission("files.favorite")||(e.favorite=void 0,e.remove_favorite=void 0),n.length>1&&(e.crop=void 0),e.properties=void 0),i.M.arrayFilter(n,(function(e){return e.preview_url})).length||(e.preview=void 0),i.M.arrayFilter(n,(function(e){return"image"===e.type})).length||(e.crop=void 0,e.alt_text=void 0),i.M.arrayFilter(n,(function(e){return e.full_url})).length||(e.copy_link=void 0),e}},{key:"_folderContextMenu",value:function(){var t=e._fileContextMenu();return t.preview=void 0,t.copy_link=void 0,t}},{key:"destroyContext",value:function(){jQuery().contextMenu&&$.contextMenu("destroy")}}],(n=null)&&a(t.prototype,n),o&&a(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}()},418:(e,t,n)=>{"use strict";n.d(t,{M:()=>h});var r=n(6294);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,l(r.key),r)}}function l(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}var h=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return t=e,i=[{key:"getUrlParam",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t||(t=window.location.search);var n=new RegExp("(?:[?&]|&)"+e+"=([^&]+)","i"),r=t.match(n);return r&&r.length>1?r[1]:null}},{key:"asset",value:function(e){if("//"===e.substring(0,2)||"http://"===e.substring(0,7)||"https://"===e.substring(0,8))return e;var t="/"!==RV_MEDIA_URL.base_url.substr(-1,1)?RV_MEDIA_URL.base_url+"/":RV_MEDIA_URL.base_url;return"/"===e.substring(0,1)?t+e.substring(1):t+e}},{key:"showAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-main")).addClass("on-loading").append($("#rv_media_loading").html())}},{key:"hideAjaxLoading",value:function(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-main")).removeClass("on-loading").find(".loading-spinner").remove()}},{key:"isOnAjaxLoading",value:function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".rv-media-items")).hasClass("on-loading")}},{key:"jsonEncode",value:function(e){return void 0===e&&(e=null),JSON.stringify(e)}},{key:"jsonDecode",value:function(e,t){if(!e)return t;if("string"==typeof e){var n;try{n=$.parseJSON(e)}catch(e){n=t}return n}return e}},{key:"getRequestParams",value:function(){return window.rvMedia.options&&"modal"===window.rvMedia.options.open_in?a(a({},r.T.request_params),window.rvMedia.options):r.T.request_params}},{key:"setSelectedFile",value:function(e){void 0!==window.rvMedia.options?window.rvMedia.options.selected_file_id=e:r.T.request_params.selected_file_id=e}},{key:"getConfigs",value:function(){return r.T}},{key:"storeConfig",value:function(){localStorage.setItem("MediaConfig",e.jsonEncode(r.T))}},{key:"storeRecentItems",value:function(){localStorage.setItem("RecentItems",e.jsonEncode(r.y))}},{key:"addToRecent",value:function(t){t instanceof Array?e.each(t,(function(e){r.y.push(e)})):(r.y.push(t),this.storeRecentItems())}},{key:"getItems",value:function(){var e=[];return $(".js-media-list-title").each((function(t,n){var r=$(n),i=r.data()||{};i.index_key=r.index(),e.push(i)})),e}},{key:"getSelectedItems",value:function(){var e=[];return $(".js-media-list-title input[type=checkbox]:checked").each((function(t,n){var r=$(n).closest(".js-media-list-title"),i=r.data()||{};i.index_key=r.index(),e.push(i)})),e}},{key:"getSelectedFiles",value:function(){var e=[];return $(".js-media-list-title[data-context=file] input[type=checkbox]:checked").each((function(t,n){var r=$(n).closest(".js-media-list-title"),i=r.data()||{};i.index_key=r.index(),e.push(i)})),e}},{key:"getSelectedFolder",value:function(){var e=[];return $(".js-media-list-title[data-context=folder] input[type=checkbox]:checked").each((function(t,n){var r=$(n).closest(".js-media-list-title"),i=r.data()||{};i.index_key=r.index(),e.push(i)})),e}},{key:"isUseInModal",value:function(){return window.rvMedia&&window.rvMedia.options&&"modal"===window.rvMedia.options.open_in}},{key:"resetPagination",value:function(){RV_MEDIA_CONFIG.pagination={paged:1,posts_per_page:40,in_process_get_media:!1,has_more:!0}}},{key:"trans",value:function(e){return _.get(RV_MEDIA_CONFIG.translations,e,e)}},{key:"config",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return _.get(RV_MEDIA_CONFIG,e,t)}},{key:"hasPermission",value:function(t){return e.inArray(e.config("permissions",[]),t)}},{key:"inArray",value:function(e,t){return _.includes(e,t)}},{key:"each",value:function(e,t){return _.each(e,t)}},{key:"forEach",value:function(e,t){return _.forEach(e,t)}},{key:"arrayReject",value:function(e,t){return _.reject(e,t)}},{key:"arrayFilter",value:function(e,t){return _.filter(e,t)}},{key:"arrayFirst",value:function(e){return _.first(e)}},{key:"isArray",value:function(e){return _.isArray(e)}},{key:"isEmpty",value:function(e){return _.isEmpty(e)}},{key:"size",value:function(e){return _.size(e)}}],(n=null)&&c(t.prototype,n),i&&c(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,i}()},1994:(e,t,n)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,o(r.key),r)}}function o(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}n.d(t,{b:()=>a});var a=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},n=[{key:"showMessage",value:function(e,t){Botble.showNotice(e,t)}}],(t=null)&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}()},2555:()=>{jQuery.event.special.doubletap={bindType:"touchend",delegateType:"touchend",handle:function(e){var t=e.handleObj,n=jQuery.data(e.target),r=(new Date).getTime(),i=n.lastTouch?r-n.lastTouch:0,o=null==o?300:o;i<o&&i>30?(n.lastTouch=null,e.type=t.origType,["clientX","clientY","pageX","pageY"].forEach((function(t){e[t]=e.originalEvent.changedTouches[0][t]})),t.handler.apply(this,arguments)):n.lastTouch=r}}},3702:(e,t,n)=>{"use strict";n.d(t,{x:()=>d});var r=n(418),i=n(6294),o=n(27);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,h(r.key),r)}}function l(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function h(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}var d=function(){return l((function e(){s(this,e)}),null,[{key:"editorSelectFile",value:function(e){var t=r.M.getUrlParam("CKEditor")||r.M.getUrlParam("CKEditorFuncNum");if(window.opener&&t){var n=r.M.arrayFirst(e);window.opener.CKEDITOR.tools.callFunction(r.M.getUrlParam("CKEditorFuncNum"),n.full_url),window.opener&&window.close()}}}])}(),u=l((function e(t,n){s(this,e);var a=window.RvMediaCustomCallback||null;if("function"!=typeof a){window.rvMedia=window.rvMedia||{};var c=$("body");n=$.extend(!0,{multiple:!0,type:"*",onSelectFiles:function(e,t){}},n);var l=function(e){e.preventDefault();var t=$(e.currentTarget);$("#rv_media_modal").modal("show"),window.rvMedia.options=n,window.rvMedia.options.open_in="modal",window.rvMedia.$el=t,i.T.request_params.filter="everything",r.M.storeConfig();var a=window.rvMedia.$el.data("rv-media");void 0!==a&&a.length>0&&(a=a[0],window.rvMedia.options=$.extend(!0,window.rvMedia.options,a||{}),void 0!==a.selected_file_id?window.rvMedia.options.is_popup=!0:void 0!==window.rvMedia.options.is_popup&&(window.rvMedia.options.is_popup=void 0)),0===$("#rv_media_body .rv-media-container").length?$("#rv_media_body").load(RV_MEDIA_URL.popup,(function(e){e.error&&alert(e.message),$("#rv_media_body").removeClass("media-modal-loading").closest(".modal-content").removeClass("bb-loading"),$(document).find(".rv-media-container .js-change-action[data-type=refresh]").trigger("click"),"everything"!==r.M.getRequestParams().filter&&$(".rv-media-actions .btn.js-rv-media-change-filter-group.js-filter-by-type").hide(),o.K.destroyContext(),o.K.initContext()})):$(document).find(".rv-media-container .js-change-action[data-type=refresh]").trigger("click")};"string"==typeof t?c.off("click",t).on("click",t,l):t.off("click").on("click",l)}else a(t,n)}));window.RvMediaStandAlone=u,$(".js-insert-to-editor").off("click").on("click",(function(e){e.preventDefault();var t=r.M.getSelectedFiles();r.M.size(t)>0&&d.editorSelectFile(t)})),$.fn.rvMedia=function(e){var t=$(this);i.T.request_params.filter="everything",$(document).find(".js-insert-to-editor").prop("disabled","trash"===i.T.request_params.view_in),r.M.storeConfig();var n=window.RvMediaCustomCallback||null;"function"!=typeof n?new u(t,e):n(t,e)},document.dispatchEvent(new CustomEvent("core-media-loaded"))},4637:(e,t,n)=>{"use strict";n.d(t,{d:()=>Ze});const r="undefined"!=typeof window&&void 0!==window.document,i=r?window:{},o=!!r&&"ontouchstart"in i.document.documentElement,a=!!r&&"PointerEvent"in i,s="cropper",c=`${s}-canvas`,l=`${s}-crosshair`,h=`${s}-grid`,d=`${s}-handle`,u=`${s}-image`,p=`${s}-selection`,f=`${s}-shade`,m=`${s}-viewer`,v="select",g="move",y="scale",b="rotate",w="transform",_="none",k="n-resize",M="e-resize",x="s-resize",C="w-resize",S="ne-resize",j="nw-resize",E="se-resize",P="sw-resize",A=a?"pointerdown":o?"touchstart":"mousedown",O=a?"pointermove":o?"touchmove":"mousemove",T=a?"pointerup pointercancel":o?"touchend touchcancel":"mouseup",I="error",D="keydown",L="load",R="wheel",z="action",F="actionend",N="actionstart",B="change",q="transform";function G(e){return"string"==typeof e}const H=Number.isNaN||i.isNaN;function U(e){return"number"==typeof e&&!H(e)}function Y(e){return U(e)&&e>0&&e<1/0}function V(e){return"object"==typeof e&&null!==e}const{hasOwnProperty:W}=Object.prototype;function X(e){if(!V(e))return!1;try{const{constructor:t}=e,{prototype:n}=t;return t&&n&&W.call(n,"isPrototypeOf")}catch(e){return!1}}function K(e){return"function"==typeof e}function Z(e){return"object"==typeof e&&null!==e&&1===e.nodeType}const Q=/([a-z\d])([A-Z])/g;function J(e){return String(e).replace(Q,"$1-$2").toLowerCase()}const ee=/-[A-z\d]/g;function te(e){return e.replace(ee,(e=>e.slice(1).toUpperCase()))}const ne=/\s\s*/;function re(e,t,n,r){t.trim().split(ne).forEach((t=>{e.removeEventListener(t,n,r)}))}function ie(e,t,n,r){t.trim().split(ne).forEach((t=>{e.addEventListener(t,n,r)}))}function oe(e,t,n,r){ie(e,t,n,Object.assign(Object.assign({},r),{once:!0}))}const ae={bubbles:!0,cancelable:!0,composed:!0};const se=Promise.resolve();function ce(e){const{documentElement:t}=e.ownerDocument,n=e.getBoundingClientRect();return{left:n.left+(i.pageXOffset-t.clientLeft),top:n.top+(i.pageYOffset-t.clientTop)}}const le=/deg|g?rad|turn$/i;function he(e){const t=parseFloat(e)||0;if(0!==t){const[n="rad"]=String(e).match(le)||[];switch(n.toLowerCase()){case"deg":return t/360*(2*Math.PI);case"grad":return t/400*(2*Math.PI);case"turn":return t*(2*Math.PI)}}return t}const de="contain";function ue(e,t=de){const{aspectRatio:n}=e;let{width:r,height:i}=e;const o=Y(r),a=Y(i);if(o&&a){const e=i*n;t===de&&e>r||"cover"===t&&e<r?i=r/n:r=i*n}else o?i=r/n:a&&(r=i*n);return{width:r,height:i}}function pe(e,...t){if(0===t.length)return e;const[n,r,i,o,a,s]=e,[c,l,h,d,u,p]=t[0];return pe(e=[n*c+i*l,r*c+o*l,n*h+i*d,r*h+o*d,n*u+i*p+a,r*u+o*p+s],...t.slice(1))}const fe=/left|top|width|height/i,me="open",ve=new WeakMap,ge=new WeakMap,ye=new Map,be=i.document&&Array.isArray(i.document.adoptedStyleSheets)&&"replaceSync"in i.CSSStyleSheet.prototype;class we extends HTMLElement{get $sharedStyle(){return(this.themeColor?`:host{--theme-color: ${this.themeColor};}`:"")+":host([hidden]){display:none!important}"}constructor(){var e,t;super(),this.shadowRootMode=me,this.slottable=!0;const n=null===(t=null===(e=Object.getPrototypeOf(this))||void 0===e?void 0:e.constructor)||void 0===t?void 0:t.$name;n&&ye.set(n,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(e,t,n){if(Object.is(n,t))return;const r=te(e);let i=n;switch(typeof this[r]){case"boolean":i=null!==n&&"false"!==n;break;case"number":i=Number(n)}switch(this[r]=i,e){case"theme-color":{const e=ge.get(this),t=this.$sharedStyle;e&&t&&(be?e.replaceSync(t):e.textContent=t);break}}}$propertyChangedCallback(e,t,n){if(!Object.is(n,t))switch(e=J(e),typeof n){case"boolean":!0===n?this.hasAttribute(e)||this.setAttribute(e,""):this.removeAttribute(e);break;case"number":n=H(n)?"":String(n);default:n?this.getAttribute(e)!==n&&this.setAttribute(e,n):this.removeAttribute(e)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach((e=>{const t=te(e);let n=this[t];(function(e){return void 0===e})(n)||this.$propertyChangedCallback(t,void 0,n),Object.defineProperty(this,t,{enumerable:!0,configurable:!0,get:()=>n,set(e){const r=n;n=e,this.$propertyChangedCallback(t,r,e)}})}));const e=this.attachShadow({mode:this.shadowRootMode||me});if(this.shadowRoot||ve.set(this,e),ge.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const t=document.createElement("template");t.innerHTML=this.$template,e.appendChild(t.content)}if(this.slottable){const t=document.createElement("slot");e.appendChild(t)}}disconnectedCallback(){ge.has(this)&&ge.delete(this),ve.has(this)&&ve.delete(this)}$getTagNameOf(e){var t;return null!==(t=ye.get(e))&&void 0!==t?t:e}$setStyles(e){return Object.keys(e).forEach((t=>{let n=e[t];U(n)&&(n=0!==n&&fe.test(t)?`${n}px`:String(n)),this.style[t]=n})),this}$getShadowRoot(){return this.shadowRoot||ve.get(this)}$addStyles(e){let t;const n=this.$getShadowRoot();return be?(t=new CSSStyleSheet,t.replaceSync(e),n.adoptedStyleSheets=n.adoptedStyleSheets.concat(t)):(t=document.createElement("style"),t.textContent=e,n.appendChild(t)),t}$emit(e,t,n){return function(e,t,n,r){return e.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign(Object.assign({},ae),{detail:n}),r)))}(this,e,t,n)}$nextTick(e){return function(e,t){return t?se.then(e?t.bind(e):t):se}(this,e)}static $define(e,t){V(e)&&(t=e,e=""),e||(e=this.$name||this.name),e=J(e),r&&i.customElements&&!i.customElements.get(e)&&customElements.define(e,this,t)}}we.$version="2.0.0";class _e extends we{constructor(){super(...arguments),this.$onPointerDown=null,this.$onPointerMove=null,this.$onPointerUp=null,this.$onWheel=null,this.$wheeling=!1,this.$pointers=new Map,this.$style=':host{display:block;min-height:100px;min-width:200px;overflow:hidden;position:relative;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([background]){background-color:#fff;background-image:repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc),repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc);background-image:repeating-conic-gradient(#ccc 0 25%,#fff 0 50%);background-position:0 0,.5rem .5rem;background-size:1rem 1rem}:host([disabled]){pointer-events:none}:host([disabled]):after{bottom:0;content:"";cursor:not-allowed;display:block;left:0;pointer-events:none;position:absolute;right:0;top:0}',this.$action=_,this.background=!1,this.disabled=!1,this.scaleStep=.1,this.themeColor="#39f"}static get observedAttributes(){return super.observedAttributes.concat(["background","disabled","scale-step"])}connectedCallback(){super.connectedCallback(),this.disabled||this.$bind()}disconnectedCallback(){this.disabled||this.$unbind(),super.disconnectedCallback()}$propertyChangedCallback(e,t,n){if(!Object.is(n,t)&&(super.$propertyChangedCallback(e,t,n),"disabled"===e))n?this.$unbind():this.$bind()}$bind(){this.$onPointerDown||(this.$onPointerDown=this.$handlePointerDown.bind(this),ie(this,A,this.$onPointerDown)),this.$onPointerMove||(this.$onPointerMove=this.$handlePointerMove.bind(this),ie(this.ownerDocument,O,this.$onPointerMove)),this.$onPointerUp||(this.$onPointerUp=this.$handlePointerUp.bind(this),ie(this.ownerDocument,T,this.$onPointerUp)),this.$onWheel||(this.$onWheel=this.$handleWheel.bind(this),ie(this,R,this.$onWheel,{passive:!1,capture:!0}))}$unbind(){this.$onPointerDown&&(re(this,A,this.$onPointerDown),this.$onPointerDown=null),this.$onPointerMove&&(re(this.ownerDocument,O,this.$onPointerMove),this.$onPointerMove=null),this.$onPointerUp&&(re(this.ownerDocument,T,this.$onPointerUp),this.$onPointerUp=null),this.$onWheel&&(re(this,R,this.$onWheel,{capture:!0}),this.$onWheel=null)}$handlePointerDown(e){const{buttons:t,button:n,type:r}=e;if(this.disabled||("pointerdown"===r&&"mouse"===e.pointerType||"mousedown"===r)&&(U(t)&&1!==t||U(n)&&0!==n||e.ctrlKey))return;const{$pointers:i}=this;let o="";if(e.changedTouches)Array.from(e.changedTouches).forEach((({identifier:e,pageX:t,pageY:n})=>{i.set(e,{startX:t,startY:n,endX:t,endY:n})}));else{const{pointerId:t=0,pageX:n,pageY:r}=e;i.set(t,{startX:n,startY:r,endX:n,endY:r})}i.size>1?o=w:Z(e.target)&&(o=e.target.action||e.target.getAttribute("action")||""),!1!==this.$emit(N,{action:o,relatedEvent:e})&&(e.preventDefault(),this.$action=o,this.style.willChange="transform")}$handlePointerMove(e){const{$action:t,$pointers:n}=this;if(this.disabled||t===_||0===n.size)return;if(!1===this.$emit("actionmove",{action:t,relatedEvent:e}))return;if(e.preventDefault(),e.changedTouches)Array.from(e.changedTouches).forEach((({identifier:e,pageX:t,pageY:r})=>{const i=n.get(e);i&&Object.assign(i,{endX:t,endY:r})}));else{const{pointerId:t=0,pageX:r,pageY:i}=e,o=n.get(t);o&&Object.assign(o,{endX:r,endY:i})}const r={action:t,relatedEvent:e};if(t===w){const t=new Map(n);let i=0,o=0,a=0,s=0,c=e.pageX,l=e.pageY;n.forEach(((e,n)=>{t.delete(n),t.forEach((t=>{let n=t.startX-e.startX,r=t.startY-e.startY,h=t.endX-e.endX,d=t.endY-e.endY,u=0,p=0,f=0,m=0;if(0===n?r<0?f=2*Math.PI:r>0&&(f=Math.PI):n>0?f=Math.PI/2+Math.atan(r/n):n<0&&(f=1.5*Math.PI+Math.atan(r/n)),0===h?d<0?m=2*Math.PI:d>0&&(m=Math.PI):h>0?m=Math.PI/2+Math.atan(d/h):h<0&&(m=1.5*Math.PI+Math.atan(d/h)),m>0||f>0){const n=m-f,r=Math.abs(n);r>i&&(i=r,a=n,c=(e.startX+t.startX)/2,l=(e.startY+t.startY)/2)}if(n=Math.abs(n),r=Math.abs(r),h=Math.abs(h),d=Math.abs(d),n>0&&r>0?u=Math.sqrt(n*n+r*r):n>0?u=n:r>0&&(u=r),h>0&&d>0?p=Math.sqrt(h*h+d*d):h>0?p=h:d>0&&(p=d),u>0&&p>0){const n=(p-u)/u,r=Math.abs(n);r>o&&(o=r,s=n,c=(e.startX+t.startX)/2,l=(e.startY+t.startY)/2)}}))}));const h=i>0,d=o>0;h&&d?(r.rotate=a,r.scale=s,r.centerX=c,r.centerY=l):h?(r.action=b,r.rotate=a,r.centerX=c,r.centerY=l):d?(r.action=y,r.scale=s,r.centerX=c,r.centerY=l):r.action=_}else{const[e]=Array.from(n.values());Object.assign(r,e)}n.forEach((e=>{e.startX=e.endX,e.startY=e.endY})),r.action!==_&&this.$emit(z,r,{cancelable:!1})}$handlePointerUp(e){const{$action:t,$pointers:n}=this;if(!this.disabled&&t!==_&&!1!==this.$emit(F,{action:t,relatedEvent:e})){if(e.preventDefault(),e.changedTouches)Array.from(e.changedTouches).forEach((({identifier:e})=>{n.delete(e)}));else{const{pointerId:t=0}=e;n.delete(t)}0===n.size&&(this.style.willChange="",this.$action=_)}}$handleWheel(e){if(this.disabled)return;if(e.preventDefault(),this.$wheeling)return;this.$wheeling=!0,setTimeout((()=>{this.$wheeling=!1}),50);const t=(e.deltaY>0?-1:1)*this.scaleStep;this.$emit(z,{action:y,scale:t,relatedEvent:e},{cancelable:!1})}$setAction(e){return G(e)&&(this.$action=e),this}$toCanvas(e){return new Promise(((t,n)=>{if(!this.isConnected)return void n(new Error("The current element is not connected to the DOM."));const r=document.createElement("canvas");let i=this.offsetWidth,o=this.offsetHeight,a=1;X(e)&&(Y(e.width)||Y(e.height))&&(({width:i,height:o}=ue({aspectRatio:i/o,width:e.width,height:e.height})),a=i/this.offsetWidth),r.width=i,r.height=o;const s=this.querySelector(this.$getTagNameOf(u));s?s.$ready().then((n=>{const c=r.getContext("2d");if(c){const[t,l,h,d,u,p]=s.$getTransform();let f=u,m=p,v=n.naturalWidth,g=n.naturalHeight;1!==a&&(f*=a,m*=a,v*=a,g*=a);const y=v/2,b=g/2;c.fillStyle="transparent",c.fillRect(0,0,i,o),X(e)&&K(e.beforeDraw)&&e.beforeDraw.call(this,c,r),c.save(),c.translate(y,b),c.transform(t,l,h,d,f,m),c.translate(-y,-b),c.drawImage(n,0,0,v,g),c.restore()}t(r)})).catch(n):t(r)}))}}_e.$name=c,_e.$version="2.0.0";const $e=new WeakMap,ke=["alt","crossorigin","decoding","importance","loading","referrerpolicy","sizes","src","srcset"];class Me extends we{constructor(){super(...arguments),this.$matrix=[1,0,0,1,0,0],this.$onLoad=null,this.$onCanvasAction=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$actionStartTarget=null,this.$style=":host{display:inline-block}img{display:block;height:100%;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}",this.$image=new Image,this.initialCenterSize="contain",this.rotatable=!1,this.scalable=!1,this.skewable=!1,this.slottable=!1,this.translatable=!1}set $canvas(e){$e.set(this,e)}get $canvas(){return $e.get(this)}static get observedAttributes(){return super.observedAttributes.concat(ke,["initial-center-size","rotatable","scalable","skewable","translatable"])}attributeChangedCallback(e,t,n){Object.is(n,t)||(super.attributeChangedCallback(e,t,n),ke.includes(e)&&this.$image.setAttribute(e,n))}$propertyChangedCallback(e,t,n){if(!Object.is(n,t)&&(super.$propertyChangedCallback(e,t,n),"initialCenterSize"===e))this.$nextTick((()=>{this.$center(n)}))}connectedCallback(){super.connectedCallback();const{$image:e}=this,t=this.closest(this.$getTagNameOf(c));t&&(this.$canvas=t,this.$setStyles({display:"block",position:"absolute"}),this.$onCanvasActionStart=e=>{var t,n;this.$actionStartTarget=null===(n=null===(t=e.detail)||void 0===t?void 0:t.relatedEvent)||void 0===n?void 0:n.target},this.$onCanvasActionEnd=()=>{this.$actionStartTarget=null},this.$onCanvasAction=this.$handleAction.bind(this),ie(t,N,this.$onCanvasActionStart),ie(t,F,this.$onCanvasActionEnd),ie(t,z,this.$onCanvasAction)),this.$onLoad=this.$handleLoad.bind(this),ie(e,L,this.$onLoad),this.$getShadowRoot().appendChild(e)}disconnectedCallback(){const{$image:e,$canvas:t}=this;t&&(this.$onCanvasActionStart&&(re(t,N,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(re(t,F,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(re(t,z,this.$onCanvasAction),this.$onCanvasAction=null)),e&&this.$onLoad&&(re(e,L,this.$onLoad),this.$onLoad=null),this.$getShadowRoot().removeChild(e),super.disconnectedCallback()}$handleLoad(){const{$image:e}=this;this.$setStyles({width:e.naturalWidth,height:e.naturalHeight}),this.$canvas&&this.$center(this.initialCenterSize)}$handleAction(e){if(this.hidden||!(this.rotatable||this.scalable||this.translatable))return;const{$canvas:t}=this,{detail:n}=e;if(n){const{relatedEvent:e}=n;let{action:r}=n;switch(r!==w||this.rotatable&&this.scalable||(r=this.rotatable?b:this.scalable?y:_),r){case g:if(this.translatable){let r=null;e&&(r=e.target.closest(this.$getTagNameOf(p))),r||(r=t.querySelector(this.$getTagNameOf(p))),r&&r.multiple&&!r.active&&(r=t.querySelector(`${this.$getTagNameOf(p)}[active]`)),r&&!r.hidden&&r.movable&&!r.dynamic&&this.$actionStartTarget&&r.contains(this.$actionStartTarget)||this.$move(n.endX-n.startX,n.endY-n.startY)}break;case b:if(this.rotatable)if(e){const{x:t,y:r}=this.getBoundingClientRect();this.$rotate(n.rotate,e.clientX-t,e.clientY-r)}else this.$rotate(n.rotate);break;case y:if(this.scalable)if(e){const t=e.target.closest(this.$getTagNameOf(p));if(!t||!t.zoomable||t.zoomable&&t.dynamic){const{x:t,y:r}=this.getBoundingClientRect();this.$zoom(n.scale,e.clientX-t,e.clientY-r)}}else this.$zoom(n.scale);break;case w:if(this.rotatable&&this.scalable){const{rotate:t}=n;let{scale:r}=n;r<0?r=1/(1-r):r+=1;const i=Math.cos(t),o=Math.sin(t),[a,s,c,l]=[i*r,o*r,-o*r,i*r];if(e){const t=this.getBoundingClientRect(),n=e.clientX-t.x,r=e.clientY-t.y,[i,o,h,d]=this.$matrix,u=n-t.width/2,p=r-t.height/2,f=(u*d-h*p)/(i*d-h*o),m=(p*i-o*u)/(i*d-h*o);this.$transform(a,s,c,l,f*(1-a)+m*c,m*(1-l)+f*s)}else this.$transform(a,s,c,l,0,0)}}}}$ready(e){const{$image:t}=this,n=new Promise(((e,n)=>{const r=new Error("Failed to load the image source");if(t.complete)t.naturalWidth>0&&t.naturalHeight>0?e(t):n(r);else{const i=()=>{re(t,I,o),e(t)},o=()=>{re(t,L,i),n(r)};oe(t,L,i),oe(t,I,o)}}));return K(e)&&n.then((t=>(e(t),t))),n}$center(e){const{parentElement:t}=this;if(!t)return this;const n=t.getBoundingClientRect(),r=n.width,i=n.height,{x:o,y:a,width:s,height:c}=this.getBoundingClientRect(),l=o+s/2,h=a+c/2,d=n.x+r/2,u=n.y+i/2;if(this.$move(d-l,u-h),e&&(s!==r||c!==i)){const t=r/s,n=i/c;switch(e){case"cover":this.$scale(Math.max(t,n));break;case"contain":this.$scale(Math.min(t,n))}}return this}$move(e,t=e){if(this.translatable&&U(e)&&U(t)){const[n,r,i,o]=this.$matrix,a=(e*o-i*t)/(n*o-i*r),s=(t*n-r*e)/(n*o-i*r);this.$translate(a,s)}return this}$moveTo(e,t=e){if(this.translatable&&U(e)&&U(t)){const[n,r,i,o]=this.$matrix,a=(e*o-i*t)/(n*o-i*r),s=(t*n-r*e)/(n*o-i*r);this.$setTransform(n,r,i,o,a,s)}return this}$rotate(e,t,n){if(this.rotatable){const r=he(e),i=Math.cos(r),o=Math.sin(r),[a,s,c,l]=[i,o,-o,i];if(U(t)&&U(n)){const[e,r,i,o]=this.$matrix,{width:h,height:d}=this.getBoundingClientRect(),u=t-h/2,p=n-d/2,f=(u*o-i*p)/(e*o-i*r),m=(p*e-r*u)/(e*o-i*r);this.$transform(a,s,c,l,f*(1-a)-m*c,m*(1-l)-f*s)}else this.$transform(a,s,c,l,0,0)}return this}$zoom(e,t,n){if(!this.scalable||0===e)return this;if(e<0?e=1/(1-e):e+=1,U(t)&&U(n)){const[r,i,o,a]=this.$matrix,{width:s,height:c}=this.getBoundingClientRect(),l=t-s/2,h=n-c/2,d=(l*a-o*h)/(r*a-o*i),u=(h*r-i*l)/(r*a-o*i);this.$transform(e,0,0,e,d*(1-e),u*(1-e))}else this.$scale(e);return this}$scale(e,t=e){return this.scalable&&this.$transform(e,0,0,t,0,0),this}$skew(e,t=0){if(this.skewable){const n=he(e),r=he(t);this.$transform(1,Math.tan(r),Math.tan(n),1,0,0)}return this}$translate(e,t=e){return this.translatable&&U(e)&&U(t)&&this.$transform(1,0,0,1,e,t),this}$transform(e,t,n,r,i,o){return U(e)&&U(t)&&U(n)&&U(r)&&U(i)&&U(o)?this.$setTransform(pe(this.$matrix,[e,t,n,r,i,o])):this}$setTransform(e,t,n,r,i,o){if((this.rotatable||this.scalable||this.skewable||this.translatable)&&(Array.isArray(e)&&([e,t,n,r,i,o]=e),U(e)&&U(t)&&U(n)&&U(r)&&U(i)&&U(o))){const a=[...this.$matrix],s=[e,t,n,r,i,o];if(!1===this.$emit(q,{matrix:s,oldMatrix:a}))return this;this.$matrix=s,this.style.transform=`matrix(${s.join(", ")})`}return this}$getTransform(){return this.$matrix.slice()}$resetTransform(){return this.$setTransform([1,0,0,1,0,0])}}Me.$name=u,Me.$version="2.0.0";const xe=new WeakMap;class Ce extends we{constructor(){super(...arguments),this.$onCanvasChange=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$style=":host{display:block;height:0;left:0;outline:var(--theme-color) solid 1px;position:relative;top:0;width:0}:host([transparent]){outline-color:transparent}",this.x=0,this.y=0,this.width=0,this.height=0,this.slottable=!1,this.themeColor="rgba(0, 0, 0, 0.65)"}set $canvas(e){xe.set(this,e)}get $canvas(){return xe.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["height","width","x","y"])}connectedCallback(){super.connectedCallback();const e=this.closest(this.$getTagNameOf(c));if(e){this.$canvas=e,this.style.position="absolute";const t=e.querySelector(this.$getTagNameOf(p));t&&(this.$onCanvasActionStart=e=>{t.hidden&&e.detail.action===v&&(this.hidden=!1)},this.$onCanvasActionEnd=e=>{t.hidden&&e.detail.action===v&&(this.hidden=!0)},this.$onCanvasChange=e=>{const{x:n,y:r,width:i,height:o}=e.detail;this.$change(n,r,i,o),(t.hidden||0===n&&0===r&&0===i&&0===o)&&(this.hidden=!0)},ie(e,N,this.$onCanvasActionStart),ie(e,F,this.$onCanvasActionEnd),ie(e,B,this.$onCanvasChange))}this.$render()}disconnectedCallback(){const{$canvas:e}=this;e&&(this.$onCanvasActionStart&&(re(e,N,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(re(e,F,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasChange&&(re(e,B,this.$onCanvasChange),this.$onCanvasChange=null)),super.disconnectedCallback()}$change(e,t,n=this.width,r=this.height){return U(e)&&U(t)&&U(n)&&U(r)&&(e!==this.x||t!==this.y||n!==this.width||r!==this.height)?(this.hidden&&(this.hidden=!1),this.x=e,this.y=t,this.width=n,this.height=r,this.$render()):this}$reset(){return this.$change(0,0,0,0)}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height,outlineWidth:i.innerWidth})}}Ce.$name=f,Ce.$version="2.0.0";class Se extends we{constructor(){super(...arguments),this.$onCanvasCropEnd=null,this.$onCanvasCropStart=null,this.$style=':host{background-color:var(--theme-color);display:block}:host([action=move]),:host([action=select]){height:100%;left:0;position:absolute;top:0;width:100%}:host([action=move]){cursor:move}:host([action=select]){cursor:crosshair}:host([action$=-resize]){background-color:transparent;height:15px;position:absolute;width:15px}:host([action$=-resize]):after{background-color:var(--theme-color);content:"";display:block;height:5px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:5px}:host([action=n-resize]),:host([action=s-resize]){cursor:ns-resize;left:50%;transform:translateX(-50%);width:100%}:host([action=n-resize]){top:-8px}:host([action=s-resize]){bottom:-8px}:host([action=e-resize]),:host([action=w-resize]){cursor:ew-resize;height:100%;top:50%;transform:translateY(-50%)}:host([action=e-resize]){right:-8px}:host([action=w-resize]){left:-8px}:host([action=ne-resize]){cursor:nesw-resize;right:-8px;top:-8px}:host([action=nw-resize]){cursor:nwse-resize;left:-8px;top:-8px}:host([action=se-resize]){bottom:-8px;cursor:nwse-resize;right:-8px}:host([action=se-resize]):after{height:15px;width:15px}@media (pointer:coarse){:host([action=se-resize]):after{height:10px;width:10px}}@media (pointer:fine){:host([action=se-resize]):after{height:5px;width:5px}}:host([action=sw-resize]){bottom:-8px;cursor:nesw-resize;left:-8px}:host([plain]){background-color:transparent}',this.action=_,this.plain=!1,this.slottable=!1,this.themeColor="rgba(51, 153, 255, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["action","plain"])}}Se.$name=d,Se.$version="2.0.0";const je=new WeakMap;class Ee extends we{constructor(){super(...arguments),this.$onCanvasAction=null,this.$onCanvasActionStart=null,this.$onCanvasActionEnd=null,this.$onDocumentKeyDown=null,this.$action="",this.$actionStartTarget=null,this.$changing=!1,this.$style=':host{display:block;left:0;position:relative;right:0}:host([outlined]){outline:1px solid var(--theme-color)}:host([multiple]){outline:1px dashed hsla(0,0%,100%,.5)}:host([multiple]):after{bottom:0;content:"";cursor:pointer;display:block;left:0;position:absolute;right:0;top:0}:host([multiple][active]){outline-color:var(--theme-color);z-index:1}:host([multiple])>*{visibility:hidden}:host([multiple][active])>*{visibility:visible}:host([multiple][active]):after{display:none}',this.$initialSelection={x:0,y:0,width:0,height:0},this.x=0,this.y=0,this.width=0,this.height=0,this.aspectRatio=NaN,this.initialAspectRatio=NaN,this.initialCoverage=NaN,this.active=!1,this.linked=!1,this.dynamic=!1,this.movable=!1,this.resizable=!1,this.zoomable=!1,this.multiple=!1,this.keyboard=!1,this.outlined=!1,this.precise=!1}set $canvas(e){je.set(this,e)}get $canvas(){return je.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["active","aspect-ratio","dynamic","height","initial-aspect-ratio","initial-coverage","keyboard","linked","movable","multiple","outlined","precise","resizable","width","x","y","zoomable"])}$propertyChangedCallback(e,t,n){if(!Object.is(n,t))switch(super.$propertyChangedCallback(e,t,n),e){case"x":case"y":case"width":case"height":this.$changing||this.$nextTick((()=>{this.$change(this.x,this.y,this.width,this.height,this.aspectRatio,!0)}));break;case"aspectRatio":case"initialAspectRatio":this.$nextTick((()=>{this.$initSelection()}));break;case"initialCoverage":this.$nextTick((()=>{Y(n)&&n<=1&&this.$initSelection(!0,!0)}));break;case"keyboard":this.$nextTick((()=>{this.$canvas&&(n?this.$onDocumentKeyDown||(this.$onDocumentKeyDown=this.$handleKeyDown.bind(this),ie(this.ownerDocument,D,this.$onDocumentKeyDown)):this.$onDocumentKeyDown&&(re(this.ownerDocument,D,this.$onDocumentKeyDown),this.$onDocumentKeyDown=null))}));break;case"multiple":this.$nextTick((()=>{if(this.$canvas){const e=this.$getSelections();n?(e.forEach((e=>{e.active=!1})),this.active=!0,this.$emit(B,{x:this.x,y:this.y,width:this.width,height:this.height})):(this.active=!1,e.slice(1).forEach((e=>{this.$removeSelection(e)})))}}));break;case"precise":this.$nextTick((()=>{this.$change(this.x,this.y)}));break;case"linked":n&&(this.dynamic=!0)}}connectedCallback(){super.connectedCallback();const e=this.closest(this.$getTagNameOf(c));e?(this.$canvas=e,this.$setStyles({position:"absolute",transform:`translate(${this.x}px, ${this.y}px)`}),this.hidden||this.$render(),this.$initSelection(!0),this.$onCanvasActionStart=this.$handleActionStart.bind(this),this.$onCanvasActionEnd=this.$handleActionEnd.bind(this),this.$onCanvasAction=this.$handleAction.bind(this),ie(e,N,this.$onCanvasActionStart),ie(e,F,this.$onCanvasActionEnd),ie(e,z,this.$onCanvasAction)):this.$render()}disconnectedCallback(){const{$canvas:e}=this;e&&(this.$onCanvasActionStart&&(re(e,N,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(re(e,F,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(re(e,z,this.$onCanvasAction),this.$onCanvasAction=null)),super.disconnectedCallback()}$getSelections(){let e=[];return this.parentElement&&(e=Array.from(this.parentElement.querySelectorAll(this.$getTagNameOf(p)))),e}$initSelection(e=!1,t=!1){const{initialCoverage:n,parentElement:r}=this;if(Y(n)&&r){const i=this.aspectRatio||this.initialAspectRatio;let o=(t?0:this.width)||r.offsetWidth*n,a=(t?0:this.height)||r.offsetHeight*n;Y(i)&&({width:o,height:a}=ue({aspectRatio:i,width:o,height:a})),this.$change(this.x,this.y,o,a),e&&this.$center(),this.$initialSelection={x:this.x,y:this.y,width:this.width,height:this.height}}}$createSelection(){const e=this.cloneNode(!0);return this.hasAttribute("id")&&e.removeAttribute("id"),e.initialCoverage=NaN,this.active=!1,this.parentElement&&this.parentElement.insertBefore(e,this.nextSibling),e}$removeSelection(e=this){if(this.parentElement){const t=this.$getSelections();if(t.length>1){const n=t.indexOf(e),r=t[n+1]||t[n-1];r&&(e.active=!1,this.parentElement.removeChild(e),r.active=!0,r.$emit(B,{x:r.x,y:r.y,width:r.width,height:r.height}))}else this.$clear()}}$handleActionStart(e){var t,n;const r=null===(n=null===(t=e.detail)||void 0===t?void 0:t.relatedEvent)||void 0===n?void 0:n.target;this.$action="",this.$actionStartTarget=r,!this.hidden&&this.multiple&&!this.active&&r===this&&this.parentElement&&(this.$getSelections().forEach((e=>{e.active=!1})),this.active=!0,this.$emit(B,{x:this.x,y:this.y,width:this.width,height:this.height}))}$handleAction(e){const{currentTarget:t,detail:n}=e;if(!t||!n)return;const{relatedEvent:r}=n;let{action:i}=n;if(!i&&this.multiple&&(i=this.$action||(null==r?void 0:r.target.action),this.$action=i),!i||this.hidden&&i!==v||this.multiple&&!this.active&&i!==y)return;const o=n.endX-n.startX,a=n.endY-n.startY,{width:s,height:c}=this;let{aspectRatio:l}=this;switch(!Y(l)&&r.shiftKey&&(l=Y(s)&&Y(c)?s/c:1),i){case v:if(0!==o&&0!==a){const{$canvas:e}=this,r=ce(t);(this.multiple&&!this.hidden?this.$createSelection():this).$change(n.startX-r.left,n.startY-r.top,Math.abs(o),Math.abs(a),l),o<0?a<0?i=j:a>0&&(i=P):o>0&&(a<0?i=S:a>0&&(i=E)),e&&(e.$action=i)}break;case g:this.movable&&(this.dynamic||this.$actionStartTarget&&this.contains(this.$actionStartTarget))&&this.$move(o,a);break;case y:if(r&&this.zoomable&&(this.dynamic||this.contains(r.target))){const e=ce(t);this.$zoom(n.scale,r.pageX-e.left,r.pageY-e.top)}break;default:this.$resize(i,o,a,l)}}$handleActionEnd(){this.$action="",this.$actionStartTarget=null}$handleKeyDown(e){if(this.hidden||!this.keyboard||this.multiple&&!this.active||e.defaultPrevented)return;const{activeElement:t}=document;if(!t||!["INPUT","TEXTAREA"].includes(t.tagName)&&!["true","plaintext-only"].includes(t.contentEditable))switch(e.key){case"Backspace":e.metaKey&&(e.preventDefault(),this.$removeSelection());break;case"Delete":e.preventDefault(),this.$removeSelection();break;case"ArrowLeft":e.preventDefault(),this.$move(-1,0);break;case"ArrowRight":e.preventDefault(),this.$move(1,0);break;case"ArrowUp":e.preventDefault(),this.$move(0,-1);break;case"ArrowDown":e.preventDefault(),this.$move(0,1);break;case"+":e.preventDefault(),this.$zoom(.1);break;case"-":e.preventDefault(),this.$zoom(-.1)}}$center(){const{parentElement:e}=this;if(!e)return this;const t=(e.offsetWidth-this.width)/2,n=(e.offsetHeight-this.height)/2;return this.$change(t,n)}$move(e,t=e){return this.$moveTo(this.x+e,this.y+t)}$moveTo(e,t=e){return this.movable?this.$change(e,t):this}$resize(e,t=0,n=0,r=this.aspectRatio){if(!this.resizable)return this;const i=Y(r),{$canvas:o}=this;let{x:a,y:s,width:c,height:l}=this;switch(e){case k:s+=n,l-=n,l<0&&(e=x,l=-l,s-=l),i&&(a+=(t=n*r)/2,c-=t,c<0&&(c=-c,a-=c));break;case M:c+=t,c<0&&(e=C,c=-c,a-=c),i&&(s-=(n=t/r)/2,l+=n,l<0&&(l=-l,s-=l));break;case x:l+=n,l<0&&(e=k,l=-l,s-=l),i&&(a-=(t=n*r)/2,c+=t,c<0&&(c=-c,a-=c));break;case C:a+=t,c-=t,c<0&&(e=M,c=-c,a-=c),i&&(s+=(n=t/r)/2,l-=n,l<0&&(l=-l,s-=l));break;case S:i&&(n=-t/r),s+=n,l-=n,c+=t,c<0&&l<0?(e=P,c=-c,l=-l,a-=c,s-=l):c<0?(e=j,c=-c,a-=c):l<0&&(e=E,l=-l,s-=l);break;case j:i&&(n=t/r),a+=t,s+=n,c-=t,l-=n,c<0&&l<0?(e=E,c=-c,l=-l,a-=c,s-=l):c<0?(e=S,c=-c,a-=c):l<0&&(e=P,l=-l,s-=l);break;case E:i&&(n=t/r),c+=t,l+=n,c<0&&l<0?(e=j,c=-c,l=-l,a-=c,s-=l):c<0?(e=P,c=-c,a-=c):l<0&&(e=S,l=-l,s-=l);break;case P:i&&(n=-t/r),a+=t,c-=t,l+=n,c<0&&l<0?(e=S,c=-c,l=-l,a-=c,s-=l):c<0?(e=E,c=-c,a-=c):l<0&&(e=j,l=-l,s-=l)}return o&&o.$setAction(e),this.$change(a,s,c,l)}$zoom(e,t,n){if(!this.zoomable||0===e)return this;e<0?e=1/(1-e):e+=1;const{width:r,height:i}=this,o=r*e,a=i*e;let s=this.x,c=this.y;return U(t)&&U(n)?(s-=(o-r)*((t-this.x)/r),c-=(a-i)*((n-this.y)/i)):(s-=(o-r)/2,c-=(a-i)/2),this.$change(s,c,o,a)}$change(e,t,n=this.width,r=this.height,i=this.aspectRatio,o=!1){return this.$changing||!U(e)||!U(t)||!U(n)||!U(r)||n<0||r<0?this:(Y(i)&&({width:n,height:r}=ue({aspectRatio:i,width:n,height:r},"cover")),this.precise||(e=Math.round(e),t=Math.round(t),n=Math.round(n),r=Math.round(r)),e===this.x&&t===this.y&&n===this.width&&r===this.height&&Object.is(i,this.aspectRatio)&&!o?this:(this.hidden&&(this.hidden=!1),!1===this.$emit(B,{x:e,y:t,width:n,height:r})?this:(this.$changing=!0,this.x=e,this.y=t,this.width=n,this.height=r,this.$changing=!1,this.$render())))}$reset(){const{x:e,y:t,width:n,height:r}=this.$initialSelection;return this.$change(e,t,n,r)}$clear(){return this.$change(0,0,0,0,NaN,!0),this.hidden=!0,this}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height})}$toCanvas(e){return new Promise(((t,n)=>{if(!this.isConnected)return void n(new Error("The current element is not connected to the DOM."));const r=document.createElement("canvas");let{width:i,height:o}=this,a=1;if(X(e)&&(Y(e.width)||Y(e.height))&&(({width:i,height:o}=ue({aspectRatio:i/o,width:e.width,height:e.height})),a=i/this.width),r.width=i,r.height=o,!this.$canvas)return void t(r);const s=this.$canvas.querySelector(this.$getTagNameOf(u));s?s.$ready().then((n=>{const c=r.getContext("2d");if(c){const[t,l,h,d,u,p]=s.$getTransform(),f=-this.x,m=-this.y,v=(f*d-h*m)/(t*d-h*l),g=(m*t-l*f)/(t*d-h*l);let y=t*v+h*g+u,b=l*v+d*g+p,w=n.naturalWidth,_=n.naturalHeight;1!==a&&(y*=a,b*=a,w*=a,_*=a);const $=w/2,k=_/2;c.fillStyle="transparent",c.fillRect(0,0,i,o),X(e)&&K(e.beforeDraw)&&e.beforeDraw.call(this,c,r),c.save(),c.translate($,k),c.transform(t,l,h,d,y,b),c.translate(-$,-k),c.drawImage(n,0,0,w,_),c.restore()}t(r)})).catch(n):t(r)}))}}Ee.$name=p,Ee.$version="2.0.0";class Pe extends we{constructor(){super(...arguments),this.$style=":host{display:flex;flex-direction:column;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([bordered]){border:1px dashed var(--theme-color)}:host([covered]){bottom:0;left:0;position:absolute;right:0;top:0}:host>span{display:flex;flex:1}:host>span+span{border-top:1px dashed var(--theme-color)}:host>span>span{flex:1}:host>span>span+span{border-left:1px dashed var(--theme-color)}",this.bordered=!1,this.columns=3,this.covered=!1,this.rows=3,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["bordered","columns","covered","rows"])}$propertyChangedCallback(e,t,n){Object.is(n,t)||(super.$propertyChangedCallback(e,t,n),"rows"!==e&&"columns"!==e||this.$nextTick((()=>{this.$render()})))}connectedCallback(){super.connectedCallback(),this.$render()}$render(){const e=this.$getShadowRoot(),t=document.createDocumentFragment();for(let e=0;e<this.rows;e+=1){const e=document.createElement("span");e.setAttribute("role","row");for(let t=0;t<this.columns;t+=1){const t=document.createElement("span");t.setAttribute("role","gridcell"),e.appendChild(t)}t.appendChild(e)}e&&(e.innerHTML="",e.appendChild(t))}}Pe.$name=h,Pe.$version="2.0.0";class Ae extends we{constructor(){super(...arguments),this.$style=':host{display:inline-block;height:1em;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle;width:1em}:host:after,:host:before{background-color:var(--theme-color);content:"";display:block;position:absolute}:host:before{height:1px;left:0;top:50%;transform:translateY(-50%);width:100%}:host:after{height:100%;left:50%;top:0;transform:translateX(-50%);width:1px}:host([centered]){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}',this.centered=!1,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["centered"])}}Ae.$name=l,Ae.$version="2.0.0";const Oe=new WeakMap,Te=new WeakMap,Ie=new WeakMap,De=new WeakMap,Le="vertical";class Re extends we{constructor(){super(...arguments),this.$onSelectionChange=null,this.$onSourceImageLoad=null,this.$onSourceImageTransform=null,this.$scale=1,this.$style=":host{display:block;height:100%;overflow:hidden;position:relative;width:100%}",this.resize=Le,this.selection="",this.slottable=!1}set $image(e){Te.set(this,e)}get $image(){return Te.get(this)}set $sourceImage(e){De.set(this,e)}get $sourceImage(){return De.get(this)}set $canvas(e){Oe.set(this,e)}get $canvas(){return Oe.get(this)}set $selection(e){Ie.set(this,e)}get $selection(){return Ie.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["resize","selection"])}connectedCallback(){super.connectedCallback();let e=null;if(e=this.selection?this.ownerDocument.querySelector(this.selection):this.closest(this.$getTagNameOf(p)),Z(e)){this.$selection=e,this.$onSelectionChange=this.$handleSelectionChange.bind(this),ie(e,B,this.$onSelectionChange);const t=e.closest(this.$getTagNameOf(c));if(t){this.$canvas=t;const e=t.querySelector(this.$getTagNameOf(u));e&&(this.$sourceImage=e,this.$image=e.cloneNode(!0),this.$getShadowRoot().appendChild(this.$image),this.$onSourceImageLoad=this.$handleSourceImageLoad.bind(this),this.$onSourceImageTransform=this.$handleSourceImageTransform.bind(this),ie(e.$image,L,this.$onSourceImageLoad),ie(e,q,this.$onSourceImageTransform))}this.$render()}}disconnectedCallback(){const{$selection:e,$sourceImage:t}=this;e&&this.$onSelectionChange&&(re(e,B,this.$onSelectionChange),this.$onSelectionChange=null),t&&this.$onSourceImageLoad&&(re(t.$image,L,this.$onSourceImageLoad),this.$onSourceImageLoad=null),t&&this.$onSourceImageTransform&&(re(t,q,this.$onSourceImageTransform),this.$onSourceImageTransform=null),super.disconnectedCallback()}$handleSelectionChange(e){this.$render(e.detail)}$handleSourceImageLoad(){const{$image:e,$sourceImage:t}=this,n=e.getAttribute("src"),r=t.getAttribute("src");r&&r!==n&&(e.setAttribute("src",r),e.$ready((()=>{setTimeout((()=>{this.$render()}),50)})))}$handleSourceImageTransform(e){this.$render(void 0,e.detail.matrix)}$render(e,t){const{$canvas:n,$selection:r}=this;e||r.hidden||(e=r),(!e||0===e.x&&0===e.y&&0===e.width&&0===e.height)&&(e={x:0,y:0,width:n.offsetWidth,height:n.offsetHeight});const{x:i,y:o,width:a,height:s}=e,c={},{clientWidth:l,clientHeight:h}=this;let d=l,u=h,p=NaN;switch(this.resize){case"both":p=1,d=a,u=s,c.width=a,c.height=s;break;case"horizontal":p=s>0?h/s:0,d=a*p,c.width=d;break;case Le:p=a>0?l/a:0,u=s*p,c.height=u;break;default:l>0?p=a>0?l/a:0:h>0&&(p=s>0?h/s:0)}this.$scale=p,this.$setStyles(c),this.$sourceImage&&this.$transformImageByOffset(null!=t?t:this.$sourceImage.$getTransform(),-i,-o)}$transformImageByOffset(e,t,n){const{$image:r,$scale:i,$sourceImage:o}=this;if(o&&r&&i>=0){const[o,a,s,c,l,h]=e,d=(t*c-s*n)/(o*c-s*a),u=(n*o-a*t)/(o*c-s*a),p=o*d+s*u+l,f=a*d+c*u+h;r.$ready((e=>{this.$setStyles.call(r,{width:e.naturalWidth*i,height:e.naturalHeight*i})})),r.$setTransform(o,a,s,c,p*i,f*i)}}}Re.$name=m,Re.$version="2.0.0";const ze=/^img|canvas$/,Fe=/<(\/?(?:script|style)[^>]*)>/gi,Ne={template:'<cropper-canvas background><cropper-image rotatable scalable skewable translatable></cropper-image><cropper-shade hidden></cropper-shade><cropper-handle action="select" plain></cropper-handle><cropper-selection initial-coverage="0.5" movable resizable><cropper-grid role="grid" bordered covered></cropper-grid><cropper-crosshair centered></cropper-crosshair><cropper-handle action="move" theme-color="rgba(255, 255, 255, 0.35)"></cropper-handle><cropper-handle action="n-resize"></cropper-handle><cropper-handle action="e-resize"></cropper-handle><cropper-handle action="s-resize"></cropper-handle><cropper-handle action="w-resize"></cropper-handle><cropper-handle action="ne-resize"></cropper-handle><cropper-handle action="nw-resize"></cropper-handle><cropper-handle action="se-resize"></cropper-handle><cropper-handle action="sw-resize"></cropper-handle></cropper-selection></cropper-canvas>'};_e.$define(),Ae.$define(),Pe.$define(),Se.$define(),Me.$define(),Ee.$define(),Ce.$define(),Re.$define();class Be{constructor(e,t){if(this.options=Ne,G(e)&&(e=document.querySelector(e)),!Z(e)||!ze.test(e.localName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,t=Object.assign(Object.assign({},Ne),t),this.options=t;const{ownerDocument:n}=e;let{container:r}=t;if(r&&(G(r)&&(r=n.querySelector(r)),!Z(r)))throw new Error("The `container` option must be an element or a valid selector.");Z(r)||(r=e.parentElement?e.parentElement:n.body),this.container=r;const i=e.localName;let o="";"img"===i?({src:o}=e):"canvas"===i&&window.HTMLCanvasElement&&(o=e.toDataURL());const{template:a}=t;if(a&&G(a)){const t=document.createElement("template"),n=document.createDocumentFragment();t.innerHTML=a.replace(Fe,"&lt;$1&gt;"),n.appendChild(t.content),Array.from(n.querySelectorAll(u)).forEach((t=>{t.setAttribute("src",o),t.setAttribute("alt",e.alt||"The image to crop")})),e.parentElement?(e.style.display="none",r.insertBefore(n,e.nextSibling)):r.appendChild(n)}}getCropperCanvas(){return this.container.querySelector(c)}getCropperImage(){return this.container.querySelector(u)}getCropperSelection(){return this.container.querySelector(p)}getCropperSelections(){return this.container.querySelectorAll(p)}}Be.version="2.0.0";var qe=n(6294),Ge=n(418),He=n(1994);function Ue(e){return Ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ue(e)}function Ye(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ye=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function h(e,t,n,r){var o=t&&t.prototype instanceof g?t:g,a=Object.create(o.prototype),s=new P(r||[]);return i(a,"_invoke",{value:C(e,n,s)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var u="suspendedStart",p="suspendedYield",f="executing",m="completed",v={};function g(){}function y(){}function b(){}var w={};l(w,a,(function(){return this}));var _=Object.getPrototypeOf,$=_&&_(_(A([])));$&&$!==n&&r.call($,a)&&(w=$);var k=b.prototype=g.prototype=Object.create(w);function M(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(i,o,a,s){var c=d(e[i],e,o);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==Ue(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(h).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return o=o?o.then(i,i):i()}})}function C(t,n,r){var i=u;return function(o,a){if(i===f)throw Error("Generator is already running");if(i===m){if("throw"===o)throw a;return{value:e,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=S(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===u)throw i=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=f;var l=d(t,n,r);if("normal"===l.type){if(i=r.done?m:p,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=m,r.method="throw",r.arg=l.arg)}}}function S(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(i,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function A(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(Ue(t)+" is not iterable")}return y.prototype=b,i(k,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:y,configurable:!0}),y.displayName=l(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},M(x.prototype),l(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new x(h(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},M(k),l(k,c,"Generator"),l(k,a,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return s.type="throw",s.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;E(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:A(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function Ve(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function We(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){Ve(o,r,i,a,s,"next",e)}function s(e){Ve(o,r,i,a,s,"throw",e)}a(void 0)}))}}function Xe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ke(r.key),r)}}function Ke(e){var t=function(e,t){if("object"!=Ue(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ue(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ue(t)?t:t+""}var Ze=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return t=e,n=null,r=[{key:"handleDropdown",value:function(){var t=Ge.M.size(Ge.M.getSelectedItems());e.renderActions(),t>0?$(".rv-dropdown-actions > .dropdown-toggle").removeClass("disabled").prop("disabled",!1):$(".rv-dropdown-actions > .dropdown-toggle").addClass("disabled").prop("disabled",!0)}},{key:"handlePreview",value:function(){var e=[];Ge.M.each(Ge.M.getSelectedFiles(),(function(t){if(t.preview_url){if("document"===t.type){var n=document.createElement("iframe");n.src=t.preview_url,n.allowFullscreen=!0,n.style.width="100vh",n.style.height="100vh",e.push(n)}else e.push(t.preview_url);qe.y.push(t.id)}})),Ge.M.size(e)>0?(Botble.lightbox(e),Ge.M.storeRecentItems()):this.handleGlobalAction("download")}},{key:"renderCropImage",value:function(){var e,t=$("#rv_media_crop_image").html(),n=$("#modal_crop_image .crop-image").empty(),r=Ge.M.getSelectedItems()[0],i=$("#modal_crop_image .form-crop"),o=t.replace(/__src__/gi,r.full_url);n.append(o);var a=n.find("img")[0],s={minContainerWidth:500,minContainerHeight:550,dragMode:"move",crop:function(t){e=t.detail,i.find('input[name="image_id"]').val(r.id),i.find('input[name="crop_data"]').val(JSON.stringify(e)),l(e.height),h(e.width)}},c=new Be(a,s);i.find("#aspectRatio").on("click",(function(){c.destroy(),$(this).is(":checked")?s.aspectRatio=e.width/e.height:s.aspectRatio=null,c=new Be(a,s)})),i.find("#dataHeight").on("change",(function(){e.height=parseFloat($(this).val()),c.setData(e),l(e.height)})),i.find("#dataWidth").on("change",(function(){e.width=parseFloat($(this).val()),c.setData(e),h(e.width)}));var l=function(e){i.find("#dataHeight").val(parseInt(e))},h=function(e){i.find("#dataWidth").val(parseInt(e))}}},{key:"handleCopyLink",value:(o=We(Ye().mark((function e(){var t;return Ye().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="",Ge.M.each(Ge.M.getSelectedFiles(),(function(e){Ge.M.isEmpty(t)||(t+="\n"),t+=e.full_url})),e.next=4,Botble.copyToClipboard(t);case 4:He.b.showMessage("success",Ge.M.trans("clipboard.success"),Ge.M.trans("message.success_header"));case 5:case"end":return e.stop()}}),e)}))),function(){return o.apply(this,arguments)})},{key:"handleCopyIndirectLink",value:(i=We(Ye().mark((function e(){var t;return Ye().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="",Ge.M.each(Ge.M.getSelectedFiles(),(function(e){Ge.M.isEmpty(t)||(t+="\n"),t+=e.indirect_url})),e.next=4,Botble.copyToClipboard(t);case 4:He.b.showMessage("success",Ge.M.trans("clipboard.success"),Ge.M.trans("message.success_header"));case 5:case"end":return e.stop()}}),e)}))),function(){return i.apply(this,arguments)})},{key:"handleShare",value:function(){$("#modal_share_items").modal("show").find("form.form-alt-text").data("action",type)}},{key:"handleGlobalAction",value:function(t,n){var r=[];switch(Ge.M.each(Ge.M.getSelectedItems(),(function(e){r.push({is_folder:e.is_folder,id:e.id,full_url:e.full_url})})),t){case"rename":$("#modal_rename_items").modal("show").find("form.form-rename").data("action",t);break;case"copy_link":e.handleCopyLink().then((function(){}));break;case"copy_indirect_link":e.handleCopyIndirectLink().then((function(){}));break;case"share":$("#modal_share_items").modal("show");break;case"preview":e.handlePreview();break;case"alt_text":$("#modal_alt_text_items").modal("show").find("form.form-alt-text").data("action",t);break;case"crop":$("#modal_crop_image").modal("show").find("form.rv-form").data("action",t);break;case"trash":$("#modal_trash_items").modal("show").find("form.form-delete-items").data("action",t);break;case"delete":$("#modal_delete_items").modal("show").find("form.form-delete-items").data("action",t);break;case"empty_trash":$("#modal_empty_trash").modal("show").find("form.form-empty-trash").data("action",t);break;case"download":var i=[];Ge.M.each(Ge.M.getSelectedItems(),(function(e){Ge.M.inArray(Ge.M.getConfigs().denied_download,e.mime_type)||i.push({id:e.id,is_folder:e.is_folder})})),i.length?e.handleDownload(i):He.b.showMessage("error",Ge.M.trans("download.error"),Ge.M.trans("message.error_header"));break;case"properties":$("#modal-properties").modal("show");break;default:e.processAction({selected:r,action:t},n)}}},{key:"processAction",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;Ge.M.showAjaxLoading(),$httpClient.make().post(RV_MEDIA_URL.global_actions,e).then((function(e){var n=e.data;Ge.M.resetPagination(),He.b.showMessage("success",n.message,Ge.M.trans("message.success_header")),t&&t(n)})).catch((function(e){var n=e.response;return t&&t(n.data)})).finally((function(){return Ge.M.hideAjaxLoading()}))}},{key:"renderRenameItems",value:function(){var e=$("#rv_media_rename_item").html(),t=$("#modal_rename_items .rename-items").empty();Ge.M.each(Ge.M.getSelectedItems(),(function(n){var r=e.replace(/__icon__/gi,n.icon||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__placeholder__/gi,"Input file name").replace(/__value__/gi,n.name),i=$(r);i.data("id",n.id.toString()),i.data("is_folder",n.is_folder),i.data("name",n.name);var o=i.find('input[name="rename_physical_file"]');o.closest(".form-check").find("span").text(n.is_folder?o.data("folder-label"):o.data("file-label")),i.find('input[name="rename_physical_file"]').on("change",(function(){i.data("rename_physical_file",$(this).is(":checked"))})),t.append(i),Botble.initFieldCollapse()}))}},{key:"renderAltTextItems",value:function(){var e=$("#rv_media_alt_text_item").html(),t=$("#modal_alt_text_items .alt-text-items").empty();Ge.M.each(Ge.M.getSelectedItems(),(function(n){var r=e.replace(/__icon__/gi,n.icon||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__placeholder__/gi,"Input file alt").replace(/__value__/gi,null===n.alt?"":n.alt),i=$(r);i.data("id",n.id),i.data("alt",n.alt),t.append(i)}))}},{key:"renderShareItems",value:function(){var e=$('#modal_share_items [data-bb-value="share-result"]'),t=$('#modal_share_items select[data-bb-value="share-type"]').val();e.val("");var n=[];Ge.M.each(Ge.M.getSelectedItems(),(function(e){switch(t){case"html":n.push("image"===e.type?'<img src="'.concat(e.full_url,'" alt="').concat(e.alt,'" />'):'<a href="'.concat(e.full_url,'" target="_blank">').concat(e.alt,"</a>"));break;case"markdown":n.push(("image"===e.type?"!":"")+"[".concat(e.alt,"](").concat(e.full_url,")"));break;case"indirect_url":n.push(e.indirect_url);break;default:n.push(e.full_url)}})),e.val(n.join("\n"))}},{key:"renderActions",value:function(){var e=Ge.M.getSelectedFolder().length>0,t=$("#rv_action_item").html(),n=0,r=$(".rv-dropdown-actions .dropdown-menu");r.empty();var i=$.extend({},!0,Ge.M.getConfigs().actions_list);if(e){var o=["preview","crop","alt_text","copy_link","copy_direct_link","share"];i.basic=Ge.M.arrayReject(i.basic,(function(e){return o.includes(e.action)})),Ge.M.hasPermission("folders.create")||(i.file=Ge.M.arrayReject(i.file,(function(e){return"make_copy"===e.action}))),Ge.M.hasPermission("folders.edit")||(i.file=Ge.M.arrayReject(i.file,(function(e){return Ge.M.inArray(["rename"],e.action)})),i.user=Ge.M.arrayReject(i.user,(function(e){return Ge.M.inArray(["rename"],e.action)}))),Ge.M.hasPermission("folders.trash")||(i.other=Ge.M.arrayReject(i.other,(function(e){return Ge.M.inArray(["trash","restore"],e.action)}))),Ge.M.hasPermission("folders.destroy")||(i.other=Ge.M.arrayReject(i.other,(function(e){return Ge.M.inArray(["delete"],e.action)}))),Ge.M.hasPermission("folders.favorite")||(i.other=Ge.M.arrayReject(i.other,(function(e){return Ge.M.inArray(["favorite","remove_favorite"],e.action)})))}var a=Ge.M.getSelectedFiles();Ge.M.arrayFilter(a,(function(e){return e.preview_url})).length||(i.basic=Ge.M.arrayReject(i.basic,(function(e){return"preview"===e.action}))),Ge.M.arrayFilter(a,(function(e){return"image"===e.type})).length||(i.basic=Ge.M.arrayReject(i.basic,(function(e){return"crop"===e.action})),i.file=Ge.M.arrayReject(i.file,(function(e){return"alt_text"===e.action}))),a.length>0&&(Ge.M.hasPermission("files.create")||(i.file=Ge.M.arrayReject(i.file,(function(e){return"make_copy"===e.action}))),Ge.M.hasPermission("files.edit")||(i.file=Ge.M.arrayReject(i.file,(function(e){return Ge.M.inArray(["rename"],e.action)}))),Ge.M.hasPermission("files.trash")||(i.other=Ge.M.arrayReject(i.other,(function(e){return Ge.M.inArray(["trash","restore"],e.action)}))),Ge.M.hasPermission("files.destroy")||(i.other=Ge.M.arrayReject(i.other,(function(e){return Ge.M.inArray(["delete"],e.action)}))),Ge.M.hasPermission("files.favorite")||(i.other=Ge.M.arrayReject(i.other,(function(e){return Ge.M.inArray(["favorite","remove_favorite"],e.action)}))),a.length>1&&(i.basic=Ge.M.arrayReject(i.basic,(function(e){return"crop"===e.action})))),(!Ge.M.hasPermission("folders.edit")||a.length>0)&&(i.other=Ge.M.arrayReject(i.other,(function(e){return Ge.M.inArray(["properties"],e.action)}))),Ge.M.each(i,(function(e,i){Ge.M.each(e,(function(e,o){var a=!1;switch(Ge.M.getRequestParams().view_in){case"all_media":Ge.M.inArray(["remove_favorite","delete","restore"],e.action)&&(a=!0);break;case"recent":Ge.M.inArray(["remove_favorite","delete","restore","make_copy"],e.action)&&(a=!0);break;case"favorites":Ge.M.inArray(["favorite","delete","restore","make_copy"],e.action)&&(a=!0);break;case"trash":Ge.M.inArray(["preview","delete","restore","rename","download"],e.action)||(a=!0)}if(!a){var s=t.replace(/__action__/gi,e.action||"").replace('<i class="__icon__ dropdown-item-icon dropdown-item-icon"></i>','<span class="icon-tabler-wrapper dropdown-item-icon">__icon__</span>').replace("__icon__",'<span class="icon-tabler-wrapper dropdown-item-icon">__icon__</span>').replace("__icon__",e.icon||"").replace(/__name__/gi,Ge.M.trans("actions_list.".concat(i,".").concat(e.action))||e.name);e.icon&&(s=s.replace("media-icon","media-icon dropdown-item-icon")),!o&&n&&(s='<li role="separator" class="divider"></li>'.concat(s)),r.append(s)}})),e.length>0&&n++}))}},{key:"handleDownload",value:function(e){var t=$(".media-download-popup");t.show(),$httpClient.make().withResponseType("blob").post(RV_MEDIA_URL.download,{selected:e}).then((function(e){var t=(e.headers["content-disposition"]||"").split("filename=")[1].split(";")[0],n=URL.createObjectURL(e.data),r=document.createElement("a");r.href=n,r.download=t,document.body.appendChild(r),r.click(),r.remove(),URL.revokeObjectURL(n)})).finally((function(){t.hide(),clearTimeout(null)}))}}],n&&Xe(t.prototype,n),r&&Xe(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,o}()},6294:(e,t,n)=>{"use strict";n.d(t,{T:()=>r,y:()=>o});var r=$.parseJSON(localStorage.getItem("MediaConfig"))||{},i={app_key:RV_MEDIA_CONFIG.random_hash?RV_MEDIA_CONFIG.random_hash:"21d06709fe1d3abdf0e35ddda89c4b282",request_params:{view_type:"tiles",filter:"everything",view_in:"all_media",sort_by:"created_at-desc",folder_id:0},hide_details_pane:!1,icons:{folder:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n            <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n        </svg>'},actions_list:{basic:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>\n                    <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>\n                </svg>',name:"Preview",action:"preview",order:0,class:"rv-action-preview"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8 5v10a1 1 0 0 0 1 1h10"></path>\n                    <path d="M5 8h10a1 1 0 0 1 1 1v10"></path>\n                </svg>',name:"Crop",action:"crop",order:1,class:"rv-action-crop"}],file:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"></path>\n                    <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"></path>\n                    <path d="M16 5l3 3"></path>\n                </svg>',name:"Rename",action:"rename",order:0,class:"rv-action-rename"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"></path>\n                    <path d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"></path>\n                </svg>',name:"Make a copy",action:"make_copy",order:1,class:"rv-action-make-copy"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M15 8h.01"></path>\n                    <path d="M11 20h-4a3 3 0 0 1 -3 -3v-10a3 3 0 0 1 3 -3h10a3 3 0 0 1 3 3v4"></path>\n                    <path d="M4 15l4 -4c.928 -.893 2.072 -.893 3 0l3 3"></path>\n                    <path d="M14 14l1 -1c.31 -.298 .644 -.497 .987 -.596"></path>\n                    <path d="M18.42 15.61a2.1 2.1 0 0 1 2.97 2.97l-3.39 3.42h-3v-3l3.42 -3.39z"></path>\n                </svg>',name:"Alt text",action:"alt_text",order:2,class:"rv-action-alt-text"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 15l6 -6"></path>\n                    <path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464"></path>\n                    <path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463"></path>\n                </svg>',name:"Copy link",action:"copy_link",order:3,class:"rv-action-copy-link"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 15l6 -6"></path>\n                    <path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464"></path>\n                    <path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463"></path>\n                </svg>',name:"Copy indirect link",action:"copy_indirect_link",order:4,class:"rv-action-copy-indirect-link"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                  <path d="M6 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M18 6m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M18 18m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>\n                  <path d="M8.7 10.7l6.6 -3.4"></path>\n                  <path d="M8.7 13.3l6.6 3.4"></path>\n                </svg>',name:"Share",action:"share",order:5,class:"rv-action-share"}],user:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z"></path>\n                </svg>',name:"Favorite",action:"favorite",order:2,class:"rv-action-favorite"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z"></path>\n                </svg>',name:"Remove favorite",action:"remove_favorite",order:3,class:"rv-action-favorite"}],other:[{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                    <path d="M7 11l5 5l5 -5"></path>\n                    <path d="M12 4l0 12"></path>\n                </svg>',name:"Download",action:"download",order:0,class:"rv-action-download"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 7l16 0"></path>\n                    <path d="M10 11l0 6"></path>\n                    <path d="M14 11l0 6"></path>\n                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>\n                    <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>\n                </svg>',name:"Move to trash",action:"trash",order:1,class:"rv-action-trash"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M19 20h-10.5l-4.21 -4.3a1 1 0 0 1 0 -1.41l10 -10a1 1 0 0 1 1.41 0l5 5a1 1 0 0 1 0 1.41l-9.2 9.3"></path>\n                    <path d="M18 13.3l-6.3 -6.3"></path>\n                </svg>',name:"Delete permanently",action:"delete",order:2,class:"rv-action-delete"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M9 11l-4 4l4 4m-4 -4h11a4 4 0 0 0 0 -8h-1"></path>\n                </svg>',name:"Restore",action:"restore",order:3,class:"rv-action-restore"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-palette" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 21a9 9 0 0 1 0 -18c4.97 0 9 3.582 9 8c0 1.06 -.474 2.078 -1.318 2.828c-.844 .75 -1.989 1.172 -3.182 1.172h-2.5a2 2 0 0 0 -1 3.75a1.3 1.3 0 0 1 -1 2.25" /><path d="M8.5 10.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /><path d="M12.5 7.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /><path d="M16.5 10.5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" /></svg>',name:"Properties",action:"properties",order:4,class:"rv-action-properties"}]}};r.app_key&&r.app_key===i.app_key||(r=i),r.request_params.search="";var o=$.parseJSON(localStorage.getItem("RecentItems"))||[]}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";n(2555);var e=n(6294),t=n(418),r=n(4637),i=n(27);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,s(r.key),r)}}function s(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}var c=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.group={},this.group.list=$("#rv_media_items_list").html(),this.group.tiles=$("#rv_media_items_tiles").html(),this.item={},this.item.list=$("#rv_media_items_list_element").html(),this.item.tiles=$("#rv_media_items_tiles_element").html(),this.$groupContainer=$(".rv-media-items")},n=[{key:"renderData",value:function(e){var n,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=this,s=t.M.getConfigs(),c=a.group[t.M.getRequestParams().view_type],l=t.M.getRequestParams().view_in;switch(t.M.inArray(["all_media","public","trash","favorites","recent"],l)||(l="all_media"),l){case"all_media":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                    <path d="M7 9l5 -5l5 5"></path>\n                    <path d="M12 4l0 12"></path>\n                </svg>';break;case"public":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>\n                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>\n                </svg>';break;case"trash":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M4 7l16 0"></path>\n                    <path d="M10 11l0 6"></path>\n                    <path d="M14 11l0 6"></path>\n                    <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>\n                    <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>\n                </svg>';break;case"favorites":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M8.243 7.34l-6.38 .925l-.113 .023a1 1 0 0 0 -.44 1.684l4.622 4.499l-1.09 6.355l-.013 .11a1 1 0 0 0 1.464 .944l5.706 -3l5.693 3l.1 .046a1 1 0 0 0 1.352 -1.1l-1.091 -6.355l4.624 -4.5l.078 -.085a1 1 0 0 0 -.633 -1.62l-6.38 -.926l-2.852 -5.78a1 1 0 0 0 -1.794 0l-2.853 5.78z" stroke-width="0" fill="currentColor"></path>\n                </svg>';break;case"recent":n='<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>\n                    <path d="M12 7v5l3 3"></path>\n                </svg>'}c=c.replace(/__noItemIcon__/gi,n).replace(/__noItemTitle__/gi,t.M.trans("no_item.".concat(l,".title"))||"").replace(/__noItemMessage__/gi,t.M.trans("no_item.".concat(l,".message"))||"");var h=$(c),d=h.find("ul");o&&this.$groupContainer.find(".rv-media-grid ul").length>0&&(d=this.$groupContainer.find(".rv-media-grid ul")),t.M.size(e.folders)>0||t.M.size(e.files)>0||o?$(".rv-media-items").addClass("has-items"):$(".rv-media-items").removeClass("has-items"),t.M.forEach(e.folders,(function(e){var n=a.item[t.M.getRequestParams().view_type];n=n.replace(/__type__/gi,"folder").replace(/__id__/gi,e.id).replace(/__name__/gi,e.name||"").replace(/__size__/gi,"").replace(/__date__/gi,e.created_at||"").replace(/__thumb__/gi,'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>');var r=$(n);t.M.forEach(e,(function(e,t){r.data(t,e)})),r.data("is_folder",!0),r.data("icon",s.icons.folder),r.find(".rv-media-thumbnail").css("color",e.color),d.append(r)})),t.M.forEach(e.files,(function(e){var n=a.item[t.M.getRequestParams().view_type];n=n.replace(/__type__/gi,"file").replace(/__id__/gi,e.id).replace(/__name__/gi,e.name||"").replace(/__size__/gi,e.size||"").replace(/__date__/gi,e.created_at||""),n="list"===t.M.getRequestParams().view_type?n.replace(/__thumb__/gi,e.icon):n.replace(/__thumb__/gi,e.thumb?'<img src="'.concat(e.thumb?e.thumb:e.full_url,'" alt="').concat(e.name,'">'):e.icon);var r=$(n);r.data("is_folder",!1),t.M.forEach(e,(function(e,t){r.data(t,e)})),d.append(r)})),!1!==i&&a.$groupContainer.empty(),o&&this.$groupContainer.find(".rv-media-grid ul").length>0||a.$groupContainer.append(h),a.$groupContainer.find(".loading-spinner").remove(),r.d.handleDropdown(),$(".js-media-list-title[data-id=".concat(e.selected_file_id,"]")).trigger("click")}}],n&&a(e.prototype,n),i&&a(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,i}();function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function h(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,d(r.key),r)}}function d(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:t+""}var u=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.$detailsWrapper=$(".rv-media-main .rv-media-details"),this.descriptionItemTemplate='<div class="mb-3 rv-media-name">\n            <label class="form-label">__title__</label>\n            __url__\n        </div>',this.onlyFields=["name","alt","full_url","size","mime_type","created_at","updated_at","nothing_selected"]},(n=[{key:"renderData",value:function(e){var n=this,r=this,i="image"===e.type&&e.full_url?'<img src="'.concat(e.full_url,'" alt="').concat(e.name,'">'):e.icon,o="";t.M.forEach(e,(function(e,n){t.M.inArray(r.onlyFields,n)&&e&&(t.M.inArray(["mime_type"],n)||(o+=r.descriptionItemTemplate.replace(/__title__/gi,t.M.trans(n)).replace(/__url__/gi,e?"full_url"===n?'<div class="input-group pe-1">\n                                        <input type="text" id="file_details_url" class="form-control" value="'.concat(e,'" />\n                                        <button class="input-group-text btn btn-default js-btn-copy-to-clipboard" type="button"\n                                                data-bb-toggle="clipboard"\n                                                data-clipboard-action="copy"\n                                                data-clipboard-message="Copied"\n                                                data-clipboard-target="#file_details_url"\n                                        >\n                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-clipboard me-0" data-clipboard-icon="true" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                                               <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                                               <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>\n                                               <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>\n                                            </svg>\n                                            <svg class="icon text-success me-0 d-none" data-clipboard-success-icon="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n                                              <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                                              <path d="M5 12l5 5l10 -10"></path>\n                                            </svg>\n                                        </button>\n                                    </div>'):'<span title="'.concat(e,'">').concat(e,"</span>"):"")))})),r.$detailsWrapper.find(".rv-media-thumbnail").html(i),r.$detailsWrapper.find(".rv-media-thumbnail").css("color",e.color),r.$detailsWrapper.find(".rv-media-description").html(o);var a="";if(e.mime_type&&-1!==e.mime_type.indexOf("image")){var s=new Image;s.src=e.full_url,s.onload=function(){a+=n.descriptionItemTemplate.replace(/__title__/gi,t.M.trans("width")).replace(/__url__/gi,'<span title="'.concat(s.width,'">').concat(s.width,"px</span>")),a+=n.descriptionItemTemplate.replace(/__title__/gi,t.M.trans("height")).replace(/__url__/gi,'<span title="'.concat(s.height,'">').concat(s.height,"px</span>")),r.$detailsWrapper.find(".rv-media-description").append(a)}}}}])&&h(e.prototype,n),r&&h(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}();function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){v(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t,n){return(t=y(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,y(r.key),r)}}function y(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=p(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:t+""}var b=function(){function n(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),this.MediaList=new c,this.MediaDetails=new u,this.breadcrumbTemplate=$("#rv_media_breadcrumb_item").html()}return o=n,a=[{key:"getMedia",value:function(){var i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(void 0!==RV_MEDIA_CONFIG.pagination){if(RV_MEDIA_CONFIG.pagination.in_process_get_media)return;RV_MEDIA_CONFIG.pagination.in_process_get_media=!0}var s=this;s.getFileDetails({icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                <path d="M15 8h.01"></path>\n                <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>\n                <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>\n                <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>\n            </svg>',nothing_selected:""});var c=t.M.getRequestParams();"recent"===c.view_in&&(c=m(m({},c),{},{recent_items:e.y})),c=m(m({},c),{},!0===o?{is_popup:!0}:{is_popup:void 0}),void 0!==(c=m(m({},c),{},{onSelectFiles:void 0})).search&&""!==c.search&&void 0!==c.selected_file_id&&(c=m(m({},c),{},{selected_file_id:void 0})),c=m(m({},c),{},{load_more_file:a}),void 0!==RV_MEDIA_CONFIG.pagination&&(c=m(m({},c),{},{paged:RV_MEDIA_CONFIG.pagination.paged,posts_per_page:RV_MEDIA_CONFIG.pagination.posts_per_page})),t.M.showAjaxLoading(),$httpClient.make().get(RV_MEDIA_URL.get_media,c).then((function(e){var t=e.data;s.MediaList.renderData(t.data,i,a),s.renderBreadcrumbs(t.data.breadcrumbs),n.refreshFilter(),r.d.renderActions(),void 0!==RV_MEDIA_CONFIG.pagination&&(void 0!==RV_MEDIA_CONFIG.pagination.paged&&(RV_MEDIA_CONFIG.pagination.paged+=1),void 0!==RV_MEDIA_CONFIG.pagination.in_process_get_media&&(RV_MEDIA_CONFIG.pagination.in_process_get_media=!1),void 0!==RV_MEDIA_CONFIG.pagination.posts_per_page&&t.data.files.length+t.data.folders.length<RV_MEDIA_CONFIG.pagination.posts_per_page&&void 0!==RV_MEDIA_CONFIG.pagination.has_more&&(RV_MEDIA_CONFIG.pagination.has_more=!1))})).finally((function(){return t.M.hideAjaxLoading()}))}},{key:"getFileDetails",value:function(e){this.MediaDetails.renderData(e)}},{key:"renderBreadcrumbs",value:function(e){var n=this,r=$(".rv-media-breadcrumb .breadcrumb");r.find("li").remove(),t.M.each(e,(function(e){var t=n.breadcrumbTemplate;t=t.replace(/__name__/gi,e.name||"").replace(/__icon__/gi,(null==e?void 0:e.icon)||'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                    <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>\n                </svg>').replace(/__folderId__/gi,e.id||0),r.append($(t))})),$(".rv-media-container").attr("data-breadcrumb-count",t.M.size(e))}}],s=[{key:"refreshFilter",value:function(){var e=$(".rv-media-container"),n=t.M.getRequestParams().view_in,r=$('.rv-media-actions .btn:not([data-type="refresh"]):not([data-bs-toggle="offcanvas"])');"all_media"===n||t.M.getRequestParams().folder_id?(r.removeClass("disabled"),e.attr("data-allow-upload","true")):(r.addClass("disabled"),e.attr("data-allow-upload","false")),$(".rv-media-actions .btn.js-rv-media-change-filter-group").removeClass("disabled");var o=$('.rv-media-actions .btn[data-action="empty_trash"]');o.hide(),"trash"===n&&t.M.size(t.M.getItems())>0&&o.removeClass("d-none disabled").show(),i.K.destroyContext(),i.K.initContext(),e.attr("data-view-in",n)}}],a&&g(o.prototype,a),s&&g(o,s),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,a,s}(),w=n(1994);function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function k(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,M(r.key),r)}}function M(e){var t=function(e,t){if("object"!=_(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==_(t)?t:t+""}var x=function(){function n(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),this.MediaService=new b,$(document).on("shown.bs.modal","#modal_add_folder",(function(e){$(e.currentTarget).find("form input[type=text]").focus()}))}return r=n,o=[{key:"closeModal",value:function(){$(document).find("#modal_add_folder").modal("hide")}}],(i=[{key:"create",value:function(e){var r=this;$httpClient.make().withButtonLoading($(document).find("#modal_add_folder button[type=submit]")).post(RV_MEDIA_URL.create_folder,{parent_id:t.M.getRequestParams().folder_id,name:e}).then((function(e){var i=e.data;w.b.showMessage("success",i.message,t.M.trans("message.success_header")),t.M.resetPagination(),r.MediaService.getMedia(!0),n.closeModal()}))}},{key:"changeFolder",value:function(n){e.T.request_params.folder_id=n,t.M.storeConfig(),this.MediaService.getMedia(!0)}}])&&k(r.prototype,i),o&&k(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,i,o}();function C(e){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C(e)}function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){E(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function E(e,t,n){return(t=A(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,A(r.key),r)}}function A(e){var t=function(e,t){if("object"!=C(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=C(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==C(t)?t:t+""}var O=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.$body=$("body"),this.dropZone=null,this.uploadUrl=RV_MEDIA_URL.upload_file,this.uploadProgressBox=$(".rv-upload-progress"),this.uploadProgressContainer=$(".rv-upload-progress .rv-upload-progress-table"),this.uploadProgressTemplate=$("#rv_media_upload_progress_item").html(),this.totalQueued=1,this.MediaService=new b,this.totalError=0}return n=e,i=[{key:"formatFileSize",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1]?1e3:1024;if(Math.abs(e)<t)return e+" B";var n=["KB","MB","GB","TB","PB","EB","ZB","YB"],r=-1;do{e/=t,++r}while(Math.abs(e)>=t&&r<n.length-1);return e.toFixed(1)+" "+n[r]}}],(r=[{key:"init",value:function(){t.M.hasPermission("files.create")&&$(".rv-media-items").length>0&&this.setupDropZone(),this.handleEvents()}},{key:"setupDropZone",value:function(){var e=this,n=this.getDropZoneConfig();e.filesUpload=0,e.dropZone&&e.dropZone.destroy(),e.dropZone=new Dropzone(document.querySelector(".rv-media-items"),j(j({},n),{},{thumbnailWidth:!1,thumbnailHeight:!1,parallelUploads:1,autoQueue:!0,clickable:".js-dropzone-upload",previewsContainer:!1,sending:function(e,n,r){r.append("_token",$('meta[name="csrf-token"]').attr("content")),r.append("folder_id",t.M.getRequestParams().folder_id),r.append("view_in",t.M.getRequestParams().view_in),r.append("path",e.fullPath)},chunksUploaded:function(t,n){e.uploadProgressContainer.find(".progress-percent").html('- <span class="text-info">100%</span>'),n()},accept:function(t,n){e.filesUpload++,e.totalError=0,n()},uploadprogress:function(t,n,r){var i=r/t.size*100;t.upload.chunked&&i>99&&(i-=1);var o=(i>100?"100":parseInt(i))+"%";e.uploadProgressContainer.find("tr").eq(t.index-1).find(".progress-percent").html('- <span class="text-info">'+o+"</span>")}})),e.dropZone.on("addedfile",(function(t){t.index=e.totalQueued,e.totalQueued++})),e.dropZone.on("sending",(function(t){e.initProgress(t.name,t.size)})),e.dropZone.on("complete",(function(t){t.accepted&&e.changeProgressStatus(t),e.filesUpload=0})),e.dropZone.on("queuecomplete",(function(){t.M.resetPagination(),e.MediaService.getMedia(!0),0===e.totalError&&setTimeout((function(){$(".rv-upload-progress .close-pane").trigger("click")}),5e3)}))}},{key:"handleEvents",value:function(){var e=this;e.$body.off("click",".rv-upload-progress .close-pane").on("click",".rv-upload-progress .close-pane",(function(t){t.preventDefault(),$(".rv-upload-progress").addClass("hide-the-pane"),e.totalError=0,setTimeout((function(){$(".rv-upload-progress tr").remove(),e.totalQueued=1}),300)}))}},{key:"initProgress",value:function(t,n){var r=this.uploadProgressTemplate.replace(/__fileName__/gi,t).replace(/__fileSize__/gi,e.formatFileSize(n)).replace(/__status__/gi,"warning").replace(/__message__/gi,"Uploading...");this.checkUploadTotalProgress()&&this.uploadProgressContainer.find("tr").length>=1||(this.uploadProgressContainer.append(r),this.uploadProgressBox.removeClass("hide-the-pane"),this.uploadProgressBox.find(".table").animate({scrollTop:this.uploadProgressContainer.height()},150))}},{key:"changeProgressStatus",value:function(e){var n=this,r=n.uploadProgressContainer.find("tr:nth-child(".concat(e.index,")")),i=r.find(".file-status"),o=t.M.jsonDecode(e.xhr.responseText||"",{}),a=!0===o.error||"error"===e.status;if(n.totalError=n.totalError+(a?1:0),i.removeClass("text-success text-danger text-warning"),i.addClass(a?"text-danger":"text-success"),i.html(a?"Error":"Uploaded"),a&&r.find(".progress-percent").html(""),"error"===e.status){if(422===e.xhr.status){var s="";$.each(o.errors,(function(e,t){s+='<span class="text-danger">'.concat(t,"</span><br>")})),r.find(".file-error").html(s)}else 500===e.xhr.status&&r.find(".file-error").html('<span class="text-danger">'.concat(e.xhr.statusText,"</span>"));r.find(".progress-percent").html("")}else o.error?(r.find(".file-error").html('<span class="text-danger">'.concat(o.message,"</span>")),r.find(".progress-percent").html("")):(t.M.addToRecent(o.data.id),t.M.setSelectedFile(o.data.id))}},{key:"getDropZoneConfig",value:function(){return{url:this.uploadUrl,uploadMultiple:!RV_MEDIA_CONFIG.chunk.enabled,chunking:RV_MEDIA_CONFIG.chunk.enabled,forceChunking:!0,parallelChunkUploads:!1,chunkSize:RV_MEDIA_CONFIG.chunk.chunk_size,retryChunks:!0,retryChunksLimit:3,timeout:0,maxFilesize:RV_MEDIA_CONFIG.chunk.max_file_size,maxFiles:null}}},{key:"checkUploadTotalProgress",value:function(){return 1===this.filesUpload}}])&&P(n.prototype,r),i&&P(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();function T(e){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}function I(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */I=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function h(e,t,n,r){var o=t&&t.prototype instanceof g?t:g,a=Object.create(o.prototype),s=new P(r||[]);return i(a,"_invoke",{value:C(e,n,s)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var u="suspendedStart",p="suspendedYield",f="executing",m="completed",v={};function g(){}function y(){}function b(){}var w={};l(w,a,(function(){return this}));var _=Object.getPrototypeOf,$=_&&_(_(A([])));$&&$!==n&&r.call($,a)&&(w=$);var k=b.prototype=g.prototype=Object.create(w);function M(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(i,o,a,s){var c=d(e[i],e,o);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==T(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(h).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return o=o?o.then(i,i):i()}})}function C(t,n,r){var i=u;return function(o,a){if(i===f)throw Error("Generator is already running");if(i===m){if("throw"===o)throw a;return{value:e,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=S(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===u)throw i=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=f;var l=d(t,n,r);if("normal"===l.type){if(i=r.done?m:p,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=m,r.method="throw",r.arg=l.arg)}}}function S(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(i,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function A(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(T(t)+" is not iterable")}return y.prototype=b,i(k,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:y,configurable:!0}),y.displayName=l(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},M(x.prototype),l(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new x(h(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},M(k),l(k,c,"Generator"),l(k,a,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return s.type="throw",s.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;E(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:A(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function D(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return L(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?L(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function R(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function z(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,F(r.key),r)}}function F(e){var t=function(e,t){if("object"!=T(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=T(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==T(t)?t:t+""}var N=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.MediaService=new b,$(document).on("shown.bs.modal","#modal_download_url",(function(e){$(e.currentTarget).find(".form-download-url input[type=text]").focus()}))}return n=e,r=[{key:"download",value:(o=I().mark((function n(r,i,o){var a,s,c,l,h,d;return I().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:a=this,r=$.trim(r).split(/\r?\n/),s=0,c=!1,l=D(r),n.prev=5,d=I().mark((function e(){var n,a,c;return I().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=h.value,a="";try{a=new URL(n).pathname.split("/").pop()}catch(e){a=n}return c=i("".concat(s," / ").concat(r.length),a,n),e.next=6,new Promise((function(e,r){$httpClient.make().post(RV_MEDIA_URL.download_url,{folderId:t.M.getRequestParams().folder_id,url:n}).then((function(t){var n,r=t.data;c(!0,r.message||(null===(n=r.data)||void 0===n?void 0:n.message)),e()})).finally((function(){return o()})).catch((function(e){return r(e)}))}));case 6:s+=1;case 7:case"end":return e.stop()}}),e)})),l.s();case 8:if((h=l.n()).done){n.next=12;break}return n.delegateYield(d(),"t0",10);case 10:n.next=8;break;case 12:n.next=17;break;case 14:n.prev=14,n.t1=n.catch(5),l.e(n.t1);case 17:return n.prev=17,l.f(),n.finish(17);case 20:t.M.resetPagination(),a.MediaService.getMedia(!0),c||(e.closeModal(),w.b.showMessage("success",t.M.trans("message.success_header")));case 23:case"end":return n.stop()}}),n,this,[[5,14,17,20]])})),a=function(){var e=this,t=arguments;return new Promise((function(n,r){var i=o.apply(e,t);function a(e){R(i,n,r,a,s,"next",e)}function s(e){R(i,n,r,a,s,"throw",e)}a(void 0)}))},function(e,t,n){return a.apply(this,arguments)})}],i=[{key:"closeModal",value:function(){$(document).find("#modal_download_url").modal("hide")}}],r&&z(n.prototype,r),i&&z(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i,o,a}(),B=n(3702);function q(e){return q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},q(e)}function G(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */G=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function h(e,t,n,r){var o=t&&t.prototype instanceof g?t:g,a=Object.create(o.prototype),s=new P(r||[]);return i(a,"_invoke",{value:C(e,n,s)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=h;var u="suspendedStart",p="suspendedYield",f="executing",m="completed",v={};function g(){}function y(){}function b(){}var w={};l(w,a,(function(){return this}));var _=Object.getPrototypeOf,$=_&&_(_(A([])));$&&$!==n&&r.call($,a)&&(w=$);var k=b.prototype=g.prototype=Object.create(w);function M(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(i,o,a,s){var c=d(e[i],e,o);if("throw"!==c.type){var l=c.arg,h=l.value;return h&&"object"==q(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(h).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,s)}))}s(c.arg)}var o;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return o=o?o.then(i,i):i()}})}function C(t,n,r){var i=u;return function(o,a){if(i===f)throw Error("Generator is already running");if(i===m){if("throw"===o)throw a;return{value:e,done:!0}}for(r.method=o,r.arg=a;;){var s=r.delegate;if(s){var c=S(s,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===u)throw i=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=f;var l=d(t,n,r);if("normal"===l.type){if(i=r.done?m:p,l.arg===v)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=m,r.method="throw",r.arg=l.arg)}}}function S(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(i,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var a=o.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function A(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(q(t)+" is not iterable")}return y.prototype=b,i(k,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:y,configurable:!0}),y.displayName=l(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},M(x.prototype),l(x.prototype,s,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new x(h(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},M(k),l(k,c,"Generator"),l(k,a,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return s.type="throw",s.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),l=r.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;E(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:A(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function H(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function U(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Y(r.key),r)}}function Y(e){var t=function(e,t){if("object"!=q(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=q(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==q(t)?t:t+""}var V=function(){return n=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.MediaService=new b,this.UploadService=new O,this.FolderService=new x,this.DownloadService=new N,this.$body=$("body")},i=[{key:"init",value:function(){t.M.resetPagination(),this.setupLayout(),this.handleMediaList(),this.changeViewType(),this.changeFilter(),this.search(),this.handleActions(),this.UploadService.init(),this.handleModals(),this.scrollGetMore()}},{key:"setupLayout",value:function(){var n=$('.js-rv-media-change-filter[data-type="filter"][data-value="'.concat(t.M.getRequestParams().filter,'"]'));n.closest("button.dropdown-item").addClass("active").closest(".dropdown").find(".js-rv-media-filter-current").html("(".concat(n.html(),")"));var r=$('.js-rv-media-change-filter[data-type="view_in"][data-value="'.concat(t.M.getRequestParams().view_in,'"]'));r.closest("button.dropdown-item").addClass("active").closest(".dropdown").find(".js-rv-media-filter-current").html("(".concat(r.html(),")")),t.M.isUseInModal()&&$(".rv-media-footer").removeClass("d-none"),$('.js-rv-media-change-filter[data-type="sort_by"][data-value="'.concat(t.M.getRequestParams().sort_by,'"]')).closest("button.dropdown-item").addClass("active");var i=$("#media_details_collapse");i.prop("checked",e.T.hide_details_pane||!1),setTimeout((function(){$(".rv-media-details").show()}),300),i.on("change",(function(n){n.preventDefault(),e.T.hide_details_pane=$(n.currentTarget).is(":checked"),t.M.storeConfig()})),$(document).on("click",".js-download-action",(function(e){e.preventDefault(),$("#modal_download_url").modal("show")})),$(document).on("click",".js-create-folder-action",(function(e){e.preventDefault(),$("#modal_add_folder").modal("show")}))}},{key:"handleMediaList",value:function(){var e=this,n=!1,i=!1,o=!1;$(document).on("keyup keydown",(function(e){n=e.ctrlKey,i=e.metaKey,o=e.shiftKey})),e.$body.off("click",".js-media-list-title").on("click",".js-media-list-title",(function(a){a.preventDefault();var s=$(a.currentTarget);if(o){var c=t.M.arrayFirst(t.M.getSelectedItems());if(c){var l=c.index_key,h=s.index();$(".rv-media-items li").each((function(e,t){e>l&&e<=h&&$(t).find("input[type=checkbox]").prop("checked",!0)}))}}else n||i||s.closest(".rv-media-items").find("input[type=checkbox]").prop("checked",!1);s.find("input[type=checkbox]").prop("checked",!0),r.d.handleDropdown(),e.MediaService.getFileDetails(s.data())})).on("dblclick doubletap",".js-media-list-title",(function(n){n.preventDefault();var i=$(n.currentTarget).data();if(!0===i.is_folder)t.M.resetPagination(),e.FolderService.changeFolder(i.id);else if(t.M.isUseInModal()){if("trash"!==t.M.getConfigs().request_params.view_in){var o=t.M.getSelectedFiles();t.M.size(o)>0&&B.x.editorSelectFile(o)}}else r.d.handlePreview();return!1})).on("dblclick doubletap",".js-up-one-level",(function(e){e.preventDefault();var t=$(".rv-media-breadcrumb .breadcrumb li").length;$(".rv-media-breadcrumb .breadcrumb li:nth-child(".concat(t-1,") a")).trigger("click")})).on("contextmenu",".js-context-menu",(function(e){$(e.currentTarget).find("input[type=checkbox]").is(":checked")||$(e.currentTarget).trigger("click")})).on("click contextmenu",".rv-media-items",(function(n){t.M.size(n.target.closest(".js-context-menu"))||($('.rv-media-items input[type="checkbox"]').prop("checked",!1),r.d.handleDropdown(),e.MediaService.getFileDetails({icon:'<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                            <path d="M15 8h.01"></path>\n                            <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>\n                            <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>\n                            <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>\n                        </svg>',nothing_selected:""}))}))}},{key:"changeViewType",value:function(){var n=this;n.$body.off("click",".js-rv-media-change-view-type button").on("click",".js-rv-media-change-view-type button",(function(r){r.preventDefault();var i=$(r.currentTarget);i.hasClass("active")||(i.closest(".js-rv-media-change-view-type").find("button").removeClass("active"),i.addClass("active"),e.T.request_params.view_type=i.data("type"),"trash"===i.data("type")?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1),t.M.storeConfig(),void 0!==RV_MEDIA_CONFIG.pagination&&void 0!==RV_MEDIA_CONFIG.pagination.paged&&(RV_MEDIA_CONFIG.pagination.paged=1),n.MediaService.getMedia(!0,!1))})),$('.js-rv-media-change-view-type .btn[data-type="'.concat(t.M.getRequestParams().view_type,'"]')).trigger("click"),this.bindIntegrateModalEvents()}},{key:"changeFilter",value:function(){var n=this;n.$body.off("click",".js-rv-media-change-filter").on("click",".js-rv-media-change-filter",(function(r){if(r.preventDefault(),!t.M.isOnAjaxLoading()){var i=$(r.currentTarget),o=i.data();e.T.request_params[o.type]=o.value,window.rvMedia.options&&"view_in"===o.type&&(window.rvMedia.options.view_in=o.value),"view_in"===o.type&&(e.T.request_params.folder_id=0,"trash"===o.value?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1)),i.closest(".dropdown").find(".js-rv-media-filter-current").html("(".concat(i.html(),")")),t.M.storeConfig(),b.refreshFilter(),t.M.resetPagination(),n.MediaService.getMedia(!0),i.addClass("active"),i.siblings().removeClass("active")}}))}},{key:"search",value:function(){var n=this;$('.input-search-wrapper input[type="text"]').val(t.M.getRequestParams().search||""),n.$body.off("submit",".input-search-wrapper").on("submit",".input-search-wrapper",(function(r){r.preventDefault(),e.T.request_params.search=$(r.currentTarget).find('input[name="search"]').val(),t.M.storeConfig(),t.M.resetPagination(),n.MediaService.getMedia(!0)}))}},{key:"handleActions",value:function(){var e=this;e.$body.off("click",'.rv-media-actions .js-change-action[data-type="refresh"]').on("click",'.rv-media-actions .js-change-action[data-type="refresh"]',(function(n){n.preventDefault(),t.M.resetPagination();var r=void 0!==window.rvMedia.$el?window.rvMedia.$el.data("rv-media"):void 0;void 0!==r&&r.length>0&&void 0!==r[0].selected_file_id?e.MediaService.getMedia(!0,!0):e.MediaService.getMedia(!0,!1)})).off("click",".rv-media-items li.no-items").on("click",".rv-media-items li.no-items",(function(e){e.preventDefault(),$(".rv-media-header .rv-media-top-header .rv-media-actions .js-dropzone-upload").trigger("click")})).off("submit",".form-add-folder").on("submit",".form-add-folder",(function(t){t.preventDefault();var n=$(t.currentTarget).find('input[name="name"]'),r=n.val();return e.FolderService.create(r),n.val(""),!1})).off("click",".js-change-folder").on("click",".js-change-folder",(function(n){n.preventDefault();var r=$(n.currentTarget).data("folder");t.M.resetPagination(),e.FolderService.changeFolder(r)})).off("click",".js-files-action").on("click",".js-files-action",(function(n){n.preventDefault(),r.d.handleGlobalAction($(n.currentTarget).data("action"),(function(){t.M.resetPagination(),e.MediaService.getMedia(!0)}))})).off("submit",".form-download-url").on("submit",".form-download-url",function(){var t,n=(t=G().mark((function t(n){var r,i,o,a,s,c,l,h;return G().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n.preventDefault(),r=$("#modal_download_url"),i=r.find("#download-form-wrapper"),o=r.find("#modal-notice").empty(),a=r.find(".modal-title"),s=r.find('textarea[name="urls"]').prop("disabled",!0),c=r.find('[type="submit"]'),l=s.val(),h=[],Botble.showButtonLoading(c),i.slideUp(),t.next=13,e.DownloadService.download(l,(function(e,t,n){var r=$('\n                        <div class="p-2 text-primary">\n                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>\n                                <path d="M12 9h.01"></path>\n                                <path d="M11 12h1v4h1"></path>\n                            </svg>\n                            <span>'.concat(t,"</span>\n                        </div>\n                    "));return o.append(r).scrollTop(o[0].scrollHeight),a.html('<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                            <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                            <path d="M7 11l5 5l5 -5"></path>\n                            <path d="M12 4l0 12"></path>\n                        </svg>\n                        '.concat(a.data("downloading")," (").concat(e,")")),function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";e||h.push(n),r.find("span").text("".concat(t,": ").concat(i)),r.attr("class","py-2 text-".concat(e?"success":"danger")).find("i").attr("class",e?"icon ti ti-check-circle":"icon ti ti-x-circle")}}),(function(){i.slideDown(),s.val(h.join("\n")).prop("disabled",!1),a.html('<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>\n                            <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>\n                            <path d="M7 11l5 5l5 -5"></path>\n                            <path d="M12 4l0 12"></path>\n                        </svg>\n                        '.concat(a.data("text"),"\n                    ")),Botble.hideButtonLoading(c)}));case 13:return t.abrupt("return",!1);case 14:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(e){H(o,r,i,a,s,"next",e)}function s(e){H(o,r,i,a,s,"throw",e)}a(void 0)}))});return function(e){return n.apply(this,arguments)}}())}},{key:"handleModals",value:function(){var e=this;e.$body.on("show.bs.modal","#modal_rename_items",(function(){r.d.renderRenameItems()})),e.$body.on("show.bs.modal","#modal_alt_text_items",(function(){r.d.renderAltTextItems()})),e.$body.on("show.bs.modal","#modal_share_items",(function(){r.d.renderShareItems()})),e.$body.on("change",'#modal_share_items select[data-bb-value="share-type"]',(function(){r.d.renderShareItems()})),e.$body.on("show.bs.modal","#modal_crop_image",(function(){r.d.renderCropImage()})),e.$body.on("show.bs.modal","#modal-properties",(function(e){if(1===t.M.getSelectedItems().length){var n=$(e.currentTarget),r=t.M.getSelectedItems()[0];n.find('input[name="color"][value="'.concat(r.color,'"]')).prop("checked",!0)}})),e.$body.on("hidden.bs.modal","#modal_download_url",(function(){var e=$("#modal_download_url");e.find("textarea").val(""),e.find("#modal-notice").empty()})),e.$body.off("click",'#modal-properties button[type="submit"]').on("click",'#modal-properties button[type="submit"]',(function(n){n.preventDefault();var i=$(n.currentTarget).closest(".modal");Botble.showButtonLoading(i.find('button[type="submit"]')),r.d.processAction({action:"properties",color:i.find('input[name="color"]:checked').val(),selected:t.M.getSelectedItems().map((function(e){return e.id.toString()}))},(function(){i.modal("hide"),Botble.hideButtonLoading(i.find('button[type="submit"]')),e.MediaService.getMedia(!0)}))})),e.$body.off("submit","#modal_crop_image .form-crop").on("submit","#modal_crop_image .form-crop",(function(t){t.preventDefault();var n=$(t.currentTarget);Botble.showButtonLoading(n.find('button[type="submit"]'));var i=n.find('input[name="image_id"]').val(),o=n.find('input[name="crop_data"]').val();r.d.processAction({action:n.data("action"),imageId:i,cropData:o},(function(t){t.error||(n.closest(".modal").modal("hide"),e.MediaService.getMedia(!0)),Botble.hideButtonLoading(n.find('button[type="submit"]'))}))})),e.$body.off("submit","#modal_rename_items .form-rename").on("submit","#modal_rename_items .form-rename",(function(n){n.preventDefault();var i=[],o=$(n.currentTarget);$("#modal_rename_items .form-control").each((function(e,t){var n=$(t),r=n.closest(".mb-3").data();r.name=n.val(),i.push(r)})),Botble.showButtonLoading(o.find('button[type="submit"]')),r.d.processAction({action:o.data("action"),selected:i},(function(n){n.error?$("#modal_rename_items .mb-3").each((function(e,r){var i=$(r);t.M.inArray(n.data,i.data("id"))?i.addClass("has-error"):i.removeClass("has-error")})):(o.closest(".modal").modal("hide"),e.MediaService.getMedia(!0)),Botble.hideButtonLoading(o.find('button[type="submit"]'))}))})),e.$body.off("submit","#modal_alt_text_items .form-alt-text").on("submit","#modal_alt_text_items .form-alt-text",(function(n){n.preventDefault();var i=[],o=$(n.currentTarget);$("#modal_alt_text_items .form-control").each((function(e,t){var n=$(t),r=n.closest(".mb-3").data();r.alt=n.val(),i.push(r)})),Botble.showButtonLoading(o.find('button[type="submit"]')),r.d.processAction({action:o.data("action"),selected:i},(function(n){n.error?$("#modal_alt_text_items .mb-3").each((function(e,r){var i=$(r);t.M.inArray(n.data,i.data("id"))?i.addClass("has-error"):i.removeClass("has-error")})):(o.closest(".modal").modal("hide"),e.MediaService.getMedia(!0)),Botble.hideButtonLoading(o.find('button[type="submit"]'))}))})),e.$body.off("submit","form.form-delete-items").on("submit","form.form-delete-items",(function(n){n.preventDefault();var i=[],o=$(n.currentTarget);Botble.showButtonLoading(o.find('button[type="submit"]')),t.M.each(t.M.getSelectedItems(),(function(e){i.push({id:e.id,is_folder:e.is_folder})})),r.d.processAction({action:o.data("action"),selected:i,skip_trash:o.find('input[name="skip_trash"]').is(":checked")},(function(t){o.closest(".modal").modal("hide"),t.error||e.MediaService.getMedia(!0),o.find('input[name="skip_trash"]').prop("checked",!1),Botble.hideButtonLoading(o.find('button[type="submit"]'))}))})),e.$body.off("submit","#modal_empty_trash .form-empty-trash").on("submit","#modal_empty_trash .form-empty-trash",(function(t){t.preventDefault();var n=$(t.currentTarget);Botble.showButtonLoading(n.find('button[type="submit"]')),r.d.processAction({action:n.data("action")},(function(){n.closest(".modal").modal("hide"),e.MediaService.getMedia(!0),Botble.hideButtonLoading(n.find('button[type="submit"]'))}))})),"trash"===t.M.getRequestParams().view_in?$(document).find(".js-insert-to-editor").prop("disabled",!0):$(document).find(".js-insert-to-editor").prop("disabled",!1),this.bindIntegrateModalEvents()}},{key:"checkFileTypeSelect",value:function(e){if(void 0!==window.rvMedia.$el){var n=t.M.arrayFirst(e),r=window.rvMedia.$el.data("rv-media");if(void 0!==r&&void 0!==r[0]&&void 0!==r[0].file_type&&"undefined"!==n&&"undefined"!==n.type){if(!r[0].file_type.match(n.type))return!1;if(void 0!==r[0].ext_allowed&&$.isArray(r[0].ext_allowed)&&-1===$.inArray(n.mime_type,r[0].ext_allowed))return!1}}return!0}},{key:"bindIntegrateModalEvents",value:function(){var e=$("#rv_media_modal"),n=this;e.off("click",".js-insert-to-editor").on("click",".js-insert-to-editor",(function(r){r.preventDefault();var i=t.M.getSelectedFiles();t.M.size(i)>0&&(window.rvMedia.options.onSelectFiles(i,window.rvMedia.$el),n.checkFileTypeSelect(i)&&e.find(".btn-close").trigger("click"))})),e.off("dblclick doubletap",'.js-media-list-title[data-context="file"]').on("dblclick doubletap",'.js-media-list-title[data-context="file"]',(function(i){if(i.preventDefault(),"trash"!==t.M.getConfigs().request_params.view_in){var o=t.M.getSelectedFiles();t.M.size(o)>0&&(window.rvMedia.options.onSelectFiles(o,window.rvMedia.$el),n.checkFileTypeSelect(o)&&e.find(".btn-close").trigger("click"))}else r.d.handlePreview()}))}},{key:"scrollGetMore",value:function(){var e=this;$(".rv-media-main .rv-media-items").on("wheel scroll",(function(t){var n,r=$(t.currentTarget),i=r[0].scrollHeight;r.scrollTop()+r.innerHeight()>=i-(r.closest(".media-modal").length>0?450:150)&&null!==(n=RV_MEDIA_CONFIG.pagination)&&void 0!==n&&n.has_more&&e.MediaService.getMedia(!1,!1,!0)}))}}],i&&U(n.prototype,i),o&&U(n,o),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,i,o}();$((function(){window.rvMedia=window.rvMedia||{},(new V).init()}))})()})();
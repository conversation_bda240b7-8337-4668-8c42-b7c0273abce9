<?php

namespace Bo<PERSON>ble\Ecommerce\Widgets;

use Bo<PERSON>ble\Base\Widgets\Chart;
use Bo<PERSON>ble\Ecommerce\Models\Order;
use Bo<PERSON>ble\Ecommerce\Widgets\Traits\HasCategory;

class OrderChart extends Chart
{
    use HasCategory;

    protected int $columns = 6;

    public function getLabel(): string
    {
        return trans('plugins/ecommerce::reports.orders_chart');
    }

    public function getOptions(): array
    {
        $data = Order::query()
            ->selectRaw('count(id) as total, date_format(created_at, "' . $this->dateFormat . '") as period')
            ->whereDate('created_at', '>=', $this->startDate)
            ->whereDate('created_at', '<=', $this->endDate)
            ->where('is_finished', true)
            ->groupBy('period')
            ->pluck('total', 'period')
            ->all();

        return [
            'series' => [
                [
                    'name' => trans('plugins/ecommerce::reports.number_of_orders'),
                    'data' => array_values($data),
                ],
            ],
            'xaxis' => [
                'categories' => $this->translateCategories($data),
            ],
        ];
    }
}

<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Requests;

use Bo<PERSON>ble\Base\Rules\EmailRule;
use Bo<PERSON>ble\Base\Rules\MediaImageRule;
use Bo<PERSON>ble\Support\Http\Requests\Request;

class ReviewRequest extends Request
{
    protected function prepareForValidation(): void
    {
        $this->merge([
            'images' => array_filter($this->input('images', []) ?? []),
        ]);
    }

    public function rules(): array
    {
        return [
            'created_at' => ['required', 'date'],
            'product_id' => ['required', 'exists:ec_products,id'],
            'customer_id' => ['nullable', 'exists:ec_customers,id'],
            'customer_name' => ['nullable', 'string', 'max:100'],
            'customer_email' => ['nullable', new EmailRule(), 'max:50'],
            'star' => ['required', 'integer', 'min:1', 'max:5'],
            'comment' => ['required', 'string', 'max:5000'],
            'images' => ['nullable', 'array'],
            'images.*' => ['nullable', new MediaImageRule()],
        ];
    }
}

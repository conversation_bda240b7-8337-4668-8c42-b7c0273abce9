$((function(){"use strict";var e=$(window);$(window).on("load",(function(){$("#preloader").delay(350).fadeOut("slow"),$("body").delay(350).css({overflow:"visible"})})),e.on("scroll",(function(){$(window).scrollTop()<100?$("#header-sticky,#header-mob-sticky,#header-tab-sticky").removeClass("header-sticky"):$("#header-sticky,#header-mob-sticky,#header-tab-sticky").addClass("header-sticky")})),e.on("scroll",(function(){e.scrollTop()<245?$(".scroll-to-target").removeClass("open"):$(".scroll-to-target").addClass("open")})),Number.prototype.format_price=function(e,t){var i=window.currencies||{};e||(e=void 0!==i.number_after_dot?i.number_after_dot:2);var o="\\d(?=(\\d{"+(t||3)+"})+$)",a="",s=this;return i.show_symbol_or_title&&(a=i.symbol||i.title),i.display_big_money&&(s>=1e6&&s<1e9?(s/=1e6,a=i.million+(a?" "+a:"")):s>=1e9&&(s/=1e9,a=i.billion+(a?" "+a:""))),s=(s=(s=s.toFixed(Math.max(0,~~e))).toString().split("."))[0].toString().replace(new RegExp(o,"g"),"$&"+i.thousands_separator)+(s[1]?i.decimal_separator+s[1]:""),i.show_symbol_or_title&&(i.is_prefix_symbol?s=a+s:s+=a),s},$(".scroll-to-target").length&&$(".scroll-to-target").on("click",(function(){var e=$(this).attr("data-target");$("html, body").animate({scrollTop:$(e).offset().top},100)})),$("[data-background]").each((function(){$(this).css("background-image","url( "+$(this).attr("data-background")+"  )")})),$("[data-width]").each((function(){$(this).css("width",$(this).attr("data-width"))})),$("[data-bg-color]").each((function(){$(this).css("background-color",$(this).attr("data-bg-color"))})),$(".tp-shop-selector select").niceSelect(),$(".mobile-menu-nav ul").hide(),$(".mobile-menu-nav ul ul").each((function(){$(this).children().length&&$(this,"li:first").parent().append('<a class="mobile-menu-exapand" href="#" title="expand/collapse" style="font-size: 18px"><i class="fal fa-plus"></i></a>')})),$(".mobile-menu-exapand").on("click",(function(e){e.preventDefault(),$(this).prev("ul").slideToggle(300,(function(){})),$(this).parent().toggleClass("dropdown-opened"),$(this).toggleClass("mobile-menu-clicked")})),$(".js-tp-cat-toggle").on("click",(function(){$(".category-menu").slideToggle(500)})),$(".tp-menu-toggle").on("click",(function(){$(".tpsideinfo:not(.tpsidecategories):not(.tpsidesearch)").addClass("tp-sidebar-opened"),$(".body-overlay").addClass("opened")})),$(".tp-categories-sidebar-toggle").on("click",(function(){$(".tpsidecategories").addClass("tp-sidebar-opened"),$(".body-overlay").addClass("opened")})),$(".tp-search-sidebar-toggle").on("click",(function(){$(".tpsidesearch").addClass("tp-sidebar-opened"),$(".body-overlay").addClass("opened")})),$(".tpsideinfo__close").on("click",(function(){$(".tpsideinfo").removeClass("tp-sidebar-opened"),$(".body-overlay").removeClass("opened")})),$(".body-overlay").on("click",(function(){$(".tpsideinfo").removeClass("tp-sidebar-opened"),$(".body-overlay").removeClass("opened")})),$(".tp-cart-toggle").on("click",(function(){$(".tp-cart-info-area").addClass("tp-sidebar-opened"),$(".cartbody-overlay").addClass("opened")})),$(".tpcart__close").on("click",(function(){$(".tp-cart-info-area").removeClass("tp-sidebar-opened"),$(".cartbody-overlay").removeClass("opened")})),$(".cartbody-overlay").on("click",(function(){$(".tp-cart-info-area").removeClass("tp-sidebar-opened"),$(".cartbody-overlay").removeClass("opened")})),$("#showlogin").on("click",(function(){$("#checkout-login").slideToggle(900)})),$("#showcoupon").on("click",(function(){$("#checkout_coupon").slideToggle(900)})),$("#cbox").on("click",(function(){$("#cbox_info").slideToggle(900)})),$("#ship-box").on("click",(function(){$("#ship-box-info").slideToggle(1e3)}));var t=function(){new Swiper(".slider-active",{loop:!0,slidesPerView:1,effect:"fade",autoplay:{delay:4500,disableOnInteraction:!0},pagination:{el:".slider-pagination",clickable:!0}}),new Swiper(".greenslider-active",{loop:!0,slidesPerView:1,fade:"effect",effect:"fade",autoplay:{delay:5e3,disableOnInteraction:!1},pagination:{el:".greenslider-pagination",clickable:!0}}),new Swiper(".slidertwo-active",{loop:!0,slidesPerView:1,effect:"fade",autoplay:{delay:5500,disableOnInteraction:!1},pagination:{el:".slidertwo_pagination",clickable:!0}}),new Swiper(".sliderthree-active",{loop:!1,effect:"fade",slidesPerView:1,autoplay:{delay:6e3,disableOnInteraction:!1},pagination:{el:".tpsliderthree__pagination",clickable:!0}}),new Swiper(".shopslider-active",{loop:!0,slidesPerView:6,spaceBetween:25,centereMode:!0,autoplay:{delay:3500,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:6},1200:{slidesPerView:5},992:{slidesPerView:4},768:{slidesPerView:3},576:{slidesPerView:2},0:{slidesPerView:1}}}),new Swiper(".tp-team-active",{loop:!1,slidesPerView:4,spaceBetween:25,centereMode:!0,autoplay:{delay:3500,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:4},1200:{slidesPerView:4},992:{slidesPerView:3},768:{slidesPerView:2},576:{slidesPerView:2},0:{slidesPerView:1}}}),new Swiper(".related-product-active",{loop:!1,slidesPerView:5,spaceBetween:30,autoplay:{delay:3500,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:5},1200:{slidesPerView:5},992:{slidesPerView:4},768:{slidesPerView:2},576:{slidesPerView:2},0:{slidesPerView:1}},navigation:{nextEl:".tprelated__nxt",prevEl:".tprelated__prv"}}),new Swiper(".product-active",{loop:!0,slidesPerView:5,spaceBetween:30,autoplay:{delay:3500,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:5},1200:{slidesPerView:5},992:{slidesPerView:4},768:{slidesPerView:3},576:{slidesPerView:3},0:{slidesPerView:1}},navigation:{nextEl:".tpproductarrow__nxt",prevEl:".tpproductarrow__prv"}}),new Swiper(".brand-active",{loop:!1,slidesPerView:6,spaceBetween:30,freeMode:!0,autoplay:{delay:3e3,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:6},1200:{slidesPerView:4},992:{slidesPerView:3},768:{slidesPerView:3},576:{slidesPerView:2},0:{slidesPerView:1}}}),new Swiper(".platinam-pro-active",{loop:!1,slidesPerView:4,spaceBetween:30,autoplay:{delay:3e3,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:4},1200:{slidesPerView:3},992:{slidesPerView:2},768:{slidesPerView:2},576:{slidesPerView:1},0:{slidesPerView:1}},navigation:{nextEl:".tpplatiarrow__nxt",prevEl:".tpplatiarrow__prv"}}),new Swiper(".testi-active",{loop:!1,slidesPerView:3,spaceBetween:30,autoplay:{delay:3e3,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:3},1200:{slidesPerView:3},992:{slidesPerView:3},768:{slidesPerView:2},576:{slidesPerView:1},0:{slidesPerView:1}},navigation:{nextEl:".tptestiarrow__nxt",prevEl:".tptestiarrow__prv"}}),new Swiper(".postbox-active",{loop:!1,slidesPerView:1,spaceBetween:0,autoplay:{delay:4e3},navigation:{nextEl:".postbox-slider-button-next",prevEl:".postbox-slider-button-prev"}}),new Swiper(".swiper--top",{spaceBetween:0,centeredSlides:!0,speed:3e4,autoplay:{delay:1},loop:!1,freeMode:!0,slidesPerView:"auto",allowTouchMove:!1,disableOnInteraction:!0});var e=$(".service-active"),t=e.attr("data-sliders-per-view")?e.attr("data-sliders-per-view"):4;e.length>0&&new Swiper(".service-active",{loop:!1,slidesPerView:t,spaceBetween:30,freeMode:!0,autoplay:{delay:3e3,disableOnInteraction:!0},breakpoints:{1400:{slidesPerView:t},900:{slidesPerView:2},576:{slidesPerView:1}}}),$(".flash-sale-slider").length&&new Swiper(".flash-sale-slider",{slidesPerView:5,spaceBetween:10,slidesPerColumn:2,slidesPerColumnFill:"row",breakpoints:{1200:{slidesPerView:5},992:{slidesPerView:4},768:{slidesPerView:3},576:{slidesPerView:2},0:{slidesPerView:1}}})},i=function(){$.fn.countdown&&(console.log("init countdown"),$(document).find("[data-countdown]").each((function(){var e=$(this),t=$(this).data("countdown");e.countdown(t,(function(t){e.html(t.strftime('<span class="cdown days"><span class="time-count">%-D</span> <p>Days</p></span> <span class="cdown hour"><span class="time-count">%-H</span> <p>Hour</p></span> <span class="cdown minutes"><span class="time-count">%M</span> <p>Minute</p></span> <span class="cdown second"> <span><span class="time-count">%S</span> <p>Second</p></span>'))}))})))};i(),t(),document.addEventListener("shortcode.loaded",(function(){i(),t()})),$(".popup-video").magnificPopup({type:"iframe"}),$(".popup-image").magnificPopup({type:"image",gallery:{enabled:!0}}),$('.tpproduct [class*="col"]').on({mouseenter:function(){$(this).siblings().stop().css("z-index","-1")},mouseleave:function(){$(this).siblings().stop().css("z-index","1")}}),$(document).on("click",".tpproduct-details__quantity .cart-minus",(function(){var e=$(this).parent().find("input"),t=parseInt(e.val())-1;t=t<1?1:t,e.val(t),e.trigger("change")})),$(document).on("click",".tpproduct-details__quantity .cart-plus",(function(){var e=$(this).parent().find("input");e.val(parseInt(e.val())+1),e.trigger("change")})),$(document).on("submit",".newsletter-form, .subscribe-form",(function(e){e.preventDefault(),e.stopPropagation();var t=$(this),i=t.find("button[type=submit]");$.ajax({type:"POST",cache:!1,url:t.prop("action"),data:new FormData(t[0]),contentType:!1,processData:!1,beforeSend:function(){i.prop("disabled",!0),i.find("i").addClass("button-loading")},success:function(e){t.find("input[type=email]").val(""),Theme.showSuccess(e.message)},error:function(e){Theme.handleError(e)},complete:function(){"undefined"!=typeof refreshRecaptcha&&refreshRecaptcha(),i.prop("disabled",!1),i.find("i").removeClass("button-loading")}})})).on("click",".product-area #product-type-tab button",(function(e){var t=$(e.target),i=t.closest("div").data("route"),o=$(document).find(".product-area .tab-content .tab-pane"),a=$(document).find(".loading-spinner");$.ajax({url:"".concat(i,"?type=").concat(t.data("type"),"&limit=").concat(t.closest("div").data("limit")),method:"GET",dataType:"json",beforeSend:function(){return a.removeClass("d-none")},success:function(e){o.html(e.data)},error:function(e){Theme.handleError(e)},complete:function(){return a.addClass("d-none")}})})).on("submit","#contact-form",(function(e){e.preventDefault();var t=$(e.currentTarget),i=t.find("button[type=submit] > i");$.ajax({type:"POST",cache:!1,url:t.prop("action"),data:new FormData(t[0]),contentType:!1,processData:!1,beforeSend:function(){i.addClass("button-loading")},success:function(e){var i=e.error,o=e.message;i?Theme.showError(o):(t[0].reset(),Theme.showSuccess(o))},error:function(e){Theme.handleError(e)},complete:function(){"undefined"!=typeof refreshRecaptcha&&refreshRecaptcha(),i.removeClass("button-loading")}})}));var o=function(){$(document).find(".product-area #product-type-tab button[data-type]").first().trigger("click")};o(),document.addEventListener("shortcode.loaded",(function(e){"products"===e.detail.name&&o()})),$(document).on("click",".more-categories",(function(e){$(e.currentTarget).toggleClass("show"),$(".hidden-to-toggle").slideToggle(500)}))}));
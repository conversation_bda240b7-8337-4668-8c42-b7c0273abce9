@mixin bg-color($color, $opacity) {
    background-color: rgba($color, $opacity);
}

@mixin tp-placeholder {
    &::-webkit-input-placeholder {
        @content;
    }
    &:-moz-placeholder {
        @content;
    }
    &::-moz-placeholder {
        @content;
    }
    &:-ms-input-placeholder {
        @content;
    }
}

@mixin filter($value) {
    -webkit-filter: $value;
    filter: $value;
}

@mixin appearance($value) {
    -webkit-appearance: $value;
    -moz-appearance: $value;
    -ms-appearance: $value;
    -o-appearance: $value;
    appearance: $value;
}

@mixin keyframes($name) {
    @-webkit-keyframes #{$name} {
        @content;
    }
    @-moz-keyframes #{$name} {
        @content;
    }
    @-ms-keyframes #{$name} {
        @content;
    }
    @keyframes #{$name} {
        @content;
    }
}

@mixin background {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

@mixin transition($time) {
    -webkit-transition: all $time ease-out 0s;
    -moz-transition: all $time ease-out 0s;
    -ms-transition: all $time ease-out 0s;
    -o-transition: all $time ease-out 0s;
    transition: all $time ease-out 0s;
}

@mixin transform($transforms) {
    -webkit-transform: $transforms;
    -moz-transform: $transforms;
    -ms-transform: $transforms;
    -o-transform: $transforms;
    transform: $transforms;
}

@mixin border-radius($man) {
    -webkit-border-radius: $man;
    -moz-border-radius: $man;
    -o-border-radius: $man;
    -ms-border-radius: $man;
    border-radius: $man;
}

@mixin sentence-case() {
    text-transform: lowercase;
    &:first-letter {
        text-transform: uppercase;
    }
}

@mixin flexbox() {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}

@mixin box-shadow($shadow) {
    -webkit-box-shadow: $shadow;
    -moz-box-shadow: $shadow;
    -ms-box-shadow: $shadow;
    -o-box-shadow: $shadow;
    box-shadow: $shadow;
}

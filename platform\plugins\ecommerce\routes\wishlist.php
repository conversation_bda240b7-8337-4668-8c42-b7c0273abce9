<?php

use <PERSON><PERSON><PERSON>\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Http\Controllers\Fronts\WishlistController;
use Bo<PERSON>ble\Ecommerce\Http\Middleware\CheckWishlistEnabledMiddleware;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;

Theme::registerRoutes(function (): void {
    Route::middleware(CheckWishlistEnabledMiddleware::class)
        ->controller(WishlistController::class)
        ->prefix(EcommerceHelper::getPageSlug('wishlist'))
        ->name('public.')
        ->group(function (): void {
            Route::get('/{code?}', 'index')->name('wishlist');
            Route::post('{productId}', 'store')->name('wishlist.add')->wherePrimary<PERSON>ey('productId');
            Route::delete('{productId}', 'destroy')->name('wishlist.remove')->whereP<PERSON><PERSON><PERSON><PERSON>('productId');
        });
});

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table): void {
            $table->id();
            $table->string('name', 120);
            $table->string('slug', 120)->unique()->nullable();
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });

        Schema::create('menu_nodes', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('menu_id')->index();
            $table->foreignId('parent_id')->default(0)->index();
            $table->foreignId('reference_id')->nullable();
            $table->string('reference_type')->nullable();
            $table->string('url', 120)->nullable();
            $table->string('icon_font', 50)->nullable();
            $table->tinyInteger('position')->unsigned()->default(0);
            $table->string('title', 120)->nullable();
            $table->string('css_class', 120)->nullable();
            $table->string('target', 20)->default('_self');
            $table->tinyInteger('has_child')->unsigned()->default(0);
            $table->timestamps();
        });

        Schema::create('menu_locations', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('menu_id');
            $table->string('location', 120);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('menus');
        Schema::dropIfExists('menu_nodes');
        Schema::dropIfExists('menu_locations');
    }
};

<?php

if (! defined('ECOMMERCE_MODULE_SCREEN_NAME')) {
    define('ECOMMERCE_MODULE_SCREEN_NAME', 'ecommerce');
}

if (! defined('ECOMMERCE_GROUP_CACHE_KEY')) {
    define('ECOMMERCE_GROUP_CACHE_KEY', 'Bo<PERSON><PERSON>\Ecommerce\Repositories');
}

if (! defined('PRODUCT_MODULE_SCREEN_NAME')) {
    define('PRODUCT_MODULE_SCREEN_NAME', 'product');
}

if (! defined('PRODUCT_CATEGORY_MODULE_SCREEN_NAME')) {
    define('PRODUCT_CATEGORY_MODULE_SCREEN_NAME', 'product-category');
}

if (! defined('BRAND_MODULE_SCREEN_NAME')) {
    define('BRAND_MODULE_SCREEN_NAME', 'brand');
}

if (! defined('PRODUCT_COLLECTION_MODULE_SCREEN_NAME')) {
    define('PRODUCT_COLLECTION_MODULE_SCREEN_NAME', 'product-collection');
}

if (! defined('PRODUCT_LABEL_MODULE_SCREEN_NAME')) {
    define('PRODUCT_LABEL_MODULE_SCREEN_NAME', 'product-label');
}

if (! defined('CURRENCY_MODULE_SCREEN_NAME')) {
    define('CURRENCY_MODULE_SCREEN_NAME', 'currency');
}

if (! defined('PRICE_MODULE_SCREEN_NAME')) {
    define('PRICE_MODULE_SCREEN_NAME', 'price');
}

if (! defined('PRODUCT_TAG_MODULE_SCREEN_NAME')) {
    define('PRODUCT_TAG_MODULE_SCREEN_NAME', 'product-tag');
}

if (! defined('GLOBAL_OPTION_MODULE_SCREEN_NAME')) {
    define('GLOBAL_OPTION_MODULE_SCREEN_NAME', 'global-option');
}

if (! defined('PRODUCT_ATTRIBUTE_SETS_MODULE_SCREEN_NAME')) {
    define('PRODUCT_ATTRIBUTE_SETS_MODULE_SCREEN_NAME', 'product-attribute-sets');
}

if (! defined('PRODUCT_ATTRIBUTES_MODULE_SCREEN_NAME')) {
    define('PRODUCT_ATTRIBUTES_MODULE_SCREEN_NAME', 'product-attributes');
}

if (! defined('PRODUCT_VARIATIONS_MODULE_SCREEN_NAME')) {
    define('PRODUCT_VARIATIONS_MODULE_SCREEN_NAME', 'product-variations');
}

if (! defined('TAX_MODULE_SCREEN_NAME')) {
    define('TAX_MODULE_SCREEN_NAME', 'ecommerce-tax');
}

if (! defined('TAX_RULE_MODULE_SCREEN_NAME')) {
    define('TAX_RULE_MODULE_SCREEN_NAME', 'ecommerce-tax-rule');
}

if (! defined('REVIEW_MODULE_SCREEN_NAME')) {
    define('REVIEW_MODULE_SCREEN_NAME', 'plugin-review');
}

if (! defined('SHIPPING_MODULE_SCREEN_NAME')) {
    define('SHIPPING_MODULE_SCREEN_NAME', 'plugin-shipping');
}

if (! defined('SHIPPING_METHOD_MODULE_SCREEN_NAME')) {
    define('SHIPPING_METHOD_MODULE_SCREEN_NAME', 'plugin-shipping-methods');
}

if (! defined('SHIPPING_METHODS_SETTINGS_PAGE')) {
    define('SHIPPING_METHODS_SETTINGS_PAGE', 'shipping-methods-settings');
}

if (! defined('SHIPPING_RULE_ITEM_MODULE_SCREEN_NAME')) {
    define('SHIPPING_RULE_ITEM_MODULE_SCREEN_NAME', 'shipping-rule-item');
}

if (! defined('ORDER_MODULE_SCREEN_NAME')) {
    define('ORDER_MODULE_SCREEN_NAME', 'plugin-order');
}

if (! defined('ORDER_RETURN_MODULE_SCREEN_NAME')) {
    define('ORDER_RETURN_MODULE_SCREEN_NAME', 'plugin-order-return');
}

if (! defined('ORDER_INCOMPLETE_MODULE_SCREEN_NAME')) {
    define('ORDER_INCOMPLETE_MODULE_SCREEN_NAME', 'plugin-order-incomplete');
}

if (! defined('ORDER_REDIRECT_CHECKOUT')) {
    define('ORDER_REDIRECT_CHECKOUT', 'plugin-redirect-checkout');
}

if (! defined('DISCOUNT_MODULE_SCREEN_NAME')) {
    define('DISCOUNT_MODULE_SCREEN_NAME', 'discount');
}

if (! defined('CUSTOMER_MODULE_SCREEN_NAME')) {
    define('CUSTOMER_MODULE_SCREEN_NAME', 'customer');
}

if (! defined('ECOMMERCE_PRODUCT_DETAIL_RENDER')) {
    define('ECOMMERCE_PRODUCT_DETAIL_RENDER', 'product_detail_render');
}

if (! defined('ECOMMERCE_PRODUCT_DETAIL_EXTRA_HTML')) {
    define('ECOMMERCE_PRODUCT_DETAIL_EXTRA_HTML', 'product_detail_extra_html');
}

if (! defined('ECOMMERCE_ORDER_DETAIL_EXTRA_HTML')) {
    define('ECOMMERCE_ORDER_DETAIL_EXTRA_HTML', 'order_detail_extra_html');
}

if (! defined('FLASH_SALE_MODULE_SCREEN_NAME')) {
    define('FLASH_SALE_MODULE_SCREEN_NAME', 'flash-sale');
}

if (! defined('PRODUCT_LABEL_MODULE_SCREEN_NAME')) {
    define('PRODUCT_LABEL_MODULE_SCREEN_NAME', 'product-label');
}

if (! defined('RENDER_PRODUCTS_IN_CHECKOUT_PAGE')) {
    define('RENDER_PRODUCTS_IN_CHECKOUT_PAGE', 'render_products_in_checkout_page');
}

if (! defined('PROCESS_GET_CHECKOUT_SUCCESS_IN_ORDER')) {
    define('PROCESS_GET_CHECKOUT_SUCCESS_IN_ORDER', 'get_checkout_success_in_order');
}

if (! defined('HANDLE_PROCESS_ORDER_DATA_ECOMMERCE')) {
    define('HANDLE_PROCESS_ORDER_DATA_ECOMMERCE', 'process_order_data');
}

if (! defined('HANDLE_PROCESS_POST_CHECKOUT_ORDER_DATA_ECOMMERCE')) {
    define('HANDLE_PROCESS_POST_CHECKOUT_ORDER_DATA_ECOMMERCE', 'post_checkout_order');
}

if (! defined('PROCESS_GET_PAYMENT_STATUS_ORDER')) {
    define('PROCESS_GET_PAYMENT_STATUS_ORDER', 'get_payment_status');
}

if (! defined('SEND_MAIL_AFTER_PROCESS_ORDER_MULTI_DATA')) {
    define('SEND_MAIL_AFTER_PROCESS_ORDER_MULTI_DATA', 'send_mail_after_process_order_multi');
}

if (! defined('PROCESS_CHECKOUT_ORDER_DATA_ECOMMERCE')) {
    define('PROCESS_CHECKOUT_ORDER_DATA_ECOMMERCE', 'checkout_order_data');
}

if (! defined('HANDLE_POST_APPLY_COUPON_CODE_ECOMMERCE')) {
    define('HANDLE_POST_APPLY_COUPON_CODE_ECOMMERCE', 'post_apply_coupon_code');
}

if (! defined('HANDLE_POST_REMOVE_COUPON_CODE_ECOMMERCE')) {
    define('HANDLE_POST_REMOVE_COUPON_CODE_ECOMMERCE', 'post_remove_coupon_code');
}

if (! defined('PROCESS_POST_SAVE_INFORMATION_CHECKOUT_ECOMMERCE')) {
    define('PROCESS_POST_SAVE_INFORMATION_CHECKOUT_ECOMMERCE', 'post_save_information_checkout');
}

if (! defined('PROCESS_GET_CHECKOUT_RECOVER_ECOMMERCE')) {
    define('PROCESS_GET_CHECKOUT_RECOVER_ECOMMERCE', 'get_checkout_recover');
}

if (! defined('PROCESS_CHECKOUT_RULES_REQUEST_ECOMMERCE')) {
    define('PROCESS_CHECKOUT_RULES_REQUEST_ECOMMERCE', 'checkout_rules_request');
}

if (! defined('PROCESS_CHECKOUT_MESSAGES_REQUEST_ECOMMERCE')) {
    define('PROCESS_CHECKOUT_MESSAGES_REQUEST_ECOMMERCE', 'checkout_messages_request');
}

if (! defined('ACTION_AFTER_ORDER_STATUS_COMPLETED_ECOMMERCE')) {
    define('ACTION_AFTER_ORDER_STATUS_COMPLETED_ECOMMERCE', 'after_order_completed');
}

if (! defined('HANDLE_CUSTOMER_UPDATED_ECOMMERCE')) {
    define('HANDLE_CUSTOMER_UPDATED_ECOMMERCE', 'customer_update');
}

if (! defined('ACTION_BEFORE_POST_ORDER_REFUND_ECOMMERCE')) {
    define('ACTION_BEFORE_POST_ORDER_REFUND_ECOMMERCE', 'before_post_order_refund');
}

if (! defined('ACTION_AFTER_POST_ORDER_REFUNDED_ECOMMERCE')) {
    define('ACTION_AFTER_POST_ORDER_REFUNDED_ECOMMERCE', 'after_post_order_refunded');
}

if (! defined('FILTER_ECOMMERCE_PROCESS_PAYMENT')) {
    define('FILTER_ECOMMERCE_PROCESS_PAYMENT', 'filter_ecommerce_process_payment');
}

if (! defined('INVOICE_MODULE_SCREEN_NAME')) {
    define('INVOICE_MODULE_SCREEN_NAME', 'invoice');
}

if (! defined('INVOICE_PAYMENT_CREATED')) {
    define('INVOICE_PAYMENT_CREATED', 'invoice_payment_created');
}

if (! defined('ACTION_AFTER_ORDER_RETURN_STATUS_COMPLETED')) {
    define('ACTION_AFTER_ORDER_RETURN_STATUS_COMPLETED', 'after_order_return_status_completed');
}

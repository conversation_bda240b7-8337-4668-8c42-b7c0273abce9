<?php

namespace Bo<PERSON>ble\Newsletter\Http\Controllers\Settings;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Newsletter\Forms\NewsletterSettingForm;
use Bo<PERSON>ble\Newsletter\Http\Requests\Settings\NewsletterSettingRequest;
use Bo<PERSON>ble\Setting\Http\Controllers\SettingController;

class NewsletterSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/newsletter::newsletter.settings.title'));

        return NewsletterSettingForm::create()->renderForm();
    }

    public function update(NewsletterSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate($request->validated());
    }
}

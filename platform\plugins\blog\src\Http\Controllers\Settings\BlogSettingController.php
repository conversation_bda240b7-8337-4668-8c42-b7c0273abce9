<?php

namespace Bo<PERSON>ble\Blog\Http\Controllers\Settings;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Blog\Forms\Settings\BlogSettingForm;
use Bo<PERSON>ble\Blog\Http\Requests\Settings\BlogSettingRequest;
use Bo<PERSON>ble\Setting\Http\Controllers\SettingController;

class BlogSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/blog::base.settings.title'));

        return BlogSettingForm::create()->renderForm();
    }

    public function update(BlogSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate($request->validated());
    }
}

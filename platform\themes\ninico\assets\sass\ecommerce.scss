.product-attributes {
    .attribute-values > ul {
        margin: 10px 0;
        padding-inline-start: 0;
    }

    .custom-checkbox,
    .custom-radio {
        input[type='checkbox'],
        input[type='radio'] {
            display: none;
        }

        label {
            display: block;
        }

        span {
            display: block;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            position: relative;
            cursor: pointer;
        }

        input[type='checkbox']:checked ~ span,
        input[type='radio']:checked ~ span {
            &:before {
                content: '';
                display: block;
                border: 2px solid var(--tp-text-primary);
                position: absolute;
                top: -4px;
                inset-inline-start: -4px;
                inset-inline-end: -4px;
                bottom: -4px;
                border-radius: 50%;
            }
        }

        &.disabled {
            label {
                position: relative;

                &:before,
                &:after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    inset-inline-start: 50%;
                    width: 1px;
                    height: 90%;
                    background-color: var(--tp-text-primary);
                    transform-origin: 50% 50%;
                    transition: all 0.4s ease;
                    z-index: 20;
                }

                &:before {
                    transform: translate(-50%, -50%) rotate(45deg);
                }

                &:after {
                    transform: translate(-50%, -50%) rotate(-45deg);
                }
            }

            span {
                opacity: 0.9;
            }

            input[type='checkbox']:checked ~ span,
            input[type='radio']:checked ~ span {
                &:before {
                    display: none;
                }
            }
        }
    }
}

.custom-checkbox,
.custom-radio {
    input[type='checkbox'],
    input[type='radio'] {
        display: none;
    }

    label {
        display: block;
    }

    span {
        display: block;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        position: relative;
        cursor: pointer;
    }

    input[type='checkbox']:checked ~ span,
    input[type='radio']:checked ~ span {
        &:before {
            content: '';
            display: block;
            border: 2px solid var(--primary-color);
            position: absolute;
            top: -4px;
            inset-inline-start: -4px;
            inset-inline-end: -4px;
            bottom: -4px;
            border-radius: 50%;
        }
    }

    &.disabled {
        label {
            position: relative;

            &:before,
            &:after {
                content: '';
                position: absolute;
                top: 50%;
                inset-inline-start: 50%;
                width: 1px;
                height: 90%;
                background-color: var(--primary-color);
                transform-origin: 50% 50%;
                transition: all 0.4s ease;
                z-index: 20;
            }

            &:before {
                transform: translate(-50%, -50%) rotate(45deg);
            }

            &:after {
                transform: translate(-50%, -50%) rotate(-45deg);
            }
        }

        span {
            opacity: 0.9;
        }

        input[type='checkbox']:checked ~ span,
        input[type='radio']:checked ~ span {
            &:before {
                display: none;
            }
        }
    }
}

.text-swatch {
    li {
        span {
            font-size: 14px;
        }
    }
}

.color-swatch {
    li {
        display: inline-block;
        margin-bottom: 5px;
        margin-inline-end: 10px;
        vertical-align: top;
    }
}

.dropdown-swatch {
    .nice-select {
        float: none;
    }
}

.br-theme-fontawesome-stars {
    .br-widget {
        a {
            font-family: 'Font Awesome 5 Pro' !important;
            font-weight: 900 !important;
        }
    }
}

.review-images {
    &:not(.review-images-total) {
        a {
            width: 82px;
        }
    }

    a {
        display: inline-block;
        height: 82px;
        margin-bottom: 5px;
        margin-inline-end: 5px;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem !important;
        line-height: 0;

        img {
            object-fit: cover;
            border: none;
        }
    }

    .more-review-images {
        span {
            background: rgba(0, 0, 0, 0.5);
            bottom: 0;
            color: #ffffff;
            font-size: 22px;
            inset-inline-start: 0;
            position: absolute;
            inset-inline-end: 0;
            top: 0;
            z-index: 1;
            align-self: center;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    &.review-images-total {
        a {
            padding: 1.5px;
            height: fit-content;
            margin: 0;
            border: 0;
        }
    }
}

.image-viewer__item {
    position: relative;
    -webkit-transition: all 0.3s cubic-bezier(0.46, 0.03, 0.52, 0.96);
    transition: all 0.3s cubic-bezier(0.46, 0.03, 0.52, 0.96);
    text-align: initial;
    margin-inline-end: 2px;
    background-color: #eee;
    width: 70px;
    height: 70px;
    border: 1px solid #c4c6cf;
    margin-top: 5px;

    img {
        height: 100%;
        width: 100%;
        object-fit: contain;
    }
}

.image-upload__uploader-container {
    display: inline-block;

    &:hover {
        cursor: pointer;
    }

    .image-upload__uploader {
        width: 70px;
        height: 70px;
        border: 1px dashed #c4c6cf;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;

        &:hover {
            background: #d9edf7;

            .image-upload__file-input {
                cursor: pointer;
            }
        }
    }
}

.image-upload__icon {
    color: #333;
    font-size: 20px;
    margin-bottom: 8px;
}

.image-upload__text {
    color: #333;
    font-size: 10px;
    padding: 0 3px;
    text-align: center;
    line-height: 1.2;
}

.image-upload__file-input {
    position: absolute;
    inset-inline-start: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
}

.image-viewer__item {
    display: inline-block;
}

.image-viewer__list {
    display: block;
    width: 100%;
}

.image-viewer__icon-remove {
    position: absolute;
    top: -1px;
    inset-inline-end: 2px;
    z-index: 1;
    cursor: pointer;

    i {
        color: #ffffff;
        background: #848484;
        border-radius: 50%;
        display: inline-block;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        font-size: 10px;
    }
}

.image-viewer__list.is-loading {
    .loading {
        display: block !important;
    }
}

.progress + .progress {
    margin-top: 5px;
}

.comments-area {
    h6 {
        font-size: 16px;
        color: #253d4e;
        font-weight: 700;
        line-height: 1.2;
    }

    .comment-list {
        &:last-child {
            padding-bottom: 0;
        }

        .single-comment {
            margin: 0 0 15px 0;
            border: 1px solid #f2f2f2;
            border-radius: 15px;
            padding: 20px;
            -webkit-transition: 0.2s;
            transition: 0.2s;

            &:not(:last-child) {
                border-bottom: 1px solid #ececec;
            }

            .desc {
                width: 100%;
            }

            .thumb {
                width: 80px;
                flex-shrink: 0;

                img {
                    min-width: 80px;
                    max-width: 80px;
                }
            }

            &:hover {
                .reply {
                    opacity: 1;
                    -webkit-transition: 0.2s;
                    transition: 0.2s;
                }
            }
        }
    }

    .thumb {
        margin-inline-end: 20px;

        img {
            width: 70px;
            border-radius: 50%;
        }
    }
}

.form-rating-stars {
    > label {
        float: right;
        color: #999;
        cursor: pointer;
        font-size: 1em;
        padding-inline-end: 0.125em;
    }

    > input:checked ~ label,
    &:not(:checked) > label:hover,
    &:not(:checked) > label:hover ~ label {
        color: #edb867;
    }

    > input:checked + label:hover,
    > input:checked ~ label:hover,
    > label:hover ~ input:checked ~ label,
    > input:checked ~ label:hover ~ label {
        color: #edb867;
    }
}

#quick-view-popup {
    position: relative;
    width: 90%;
    max-width: 920px;
    margin: 0 auto;
    padding: 0;
    background-color: #fff;
    box-sizing: border-box;

    * {
        box-sizing: border-box;
    }

    .quickview-content {
        display: flex !important;
        float: none !important;
    }

    .product-detail {
        overflow-y: scroll;
        max-height: 460px;

        .tpproduct-details__pera {
            p {
                margin-bottom: 10px;
            }
        }

        .tpproduct-details__title-area {
            position: relative;

            .tpproduct-details__stock {
                position: absolute;
                top: -2rem;
                inset-inline-end: 13rem;
            }
        }
    }

    .thumbnails {
        outline: none;
        width: 460px;
        text-align: center;
        position: relative;
        background-color: #f9f9f9;
        margin: 0;
        max-height: 460px;

        .images {
            width: 100% !important;
            height: 100% !important;
            float: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .thumbnail {
            display: flex;
            height: 100%;
            justify-content: center;
        }

        img {
            display: block;
            object-fit: contain;
            object-position: center;
            vertical-align: middle;
            max-height: 460px;
            max-width: 100%;
            margin: auto;
            border-radius: 0;
        }

        img[data-fancybox='gallery'] {
            cursor: pointer;
        }

        .slick-slide {
            outline: none;
            max-height: 100%;
        }

        .slick-list {
            display: flex;
            width: 100%;
            height: 100%;
            max-height: 100%;
            position: relative;
        }

        .slick-track {
            display: flex;
            align-items: center;
            max-height: 460px;
        }

        .slick-dots {
            margin: 0;
            padding: 0;
            display: block;
            width: 100%;
            text-align: center;
            position: absolute;
            inset-inline-start: 0;
            bottom: 10px;
            z-index: 8;

            li {
                position: relative;
                display: inline-block;
                width: 16px;
                height: 16px;
                margin: 0;
                padding: 0;
                cursor: pointer;

                button {
                    font-size: 0;
                    line-height: 0;
                    display: block;
                    width: 16px;
                    height: 16px;
                    padding: 5px;
                    margin: 0;
                    text-align: center;
                    cursor: pointer;
                    color: transparent;
                    border: none;
                    outline: none;
                    background: transparent;
                    opacity: 0.5;

                    &:before {
                        content: '';
                        display: block;
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background-color: #000000;
                    }

                    &:focus {
                        outline: none;
                    }
                }
            }

            li.slick-active {
                button {
                    opacity: 1;
                }
            }
        }

        .slick-arrow {
            width: 44px;
            height: 44px;
            line-height: 44px;
            text-align: center;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
            overflow: hidden;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0;
            color: #222;
            background-color: transparent;
            border-radius: 0;
            border: none !important;
            z-index: 8;

            &:hover {
                color: #fff;
                background-color: rgba(0, 0, 0, 0.5);
            }

            &:before {
                font-family: 'Font Awesome 5 Pro';
                font-size: 24px;
                line-height: 44px;
                font-style: normal;
                font-weight: normal;
                font-variant: normal;
                text-transform: none;
                speak: none;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            &:focus {
                outline: none;
            }
        }

        .slick-arrow.slick-prev {
            inset-inline-start: 0;

            &:before {
                content: '\f053';
            }
        }

        .slick-arrow.slick-next {
            inset-inline-end: 0;

            &:before {
                content: '\f054';
            }
        }
    }
}

@media screen and (max-width: 1023px) {
    #quick-view-popup {
        max-width: 460px;
        max-height: 90vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 100%;
        flex-grow: 1;
        position: relative;
        overflow-y: auto;

        > .quickview-content {
            display: flex;
            flex-direction: column;

            > div {
                width: 100% !important;
                float: none !important;
                margin: 0 !important;
                height: auto;
            }

            .thumbnails {
                max-height: 338px;
            }

            .product-detail {
                max-height: unset;
                overflow-y: unset;
            }
        }

        .thumbnails {
            img {
                max-height: 338px;
                margin: 0;
            }

            .slick-track {
                max-height: 338px;
            }
        }
    }
}

.product-sidebar__list {
    max-height: unset;
    overflow: unset;

    .category-filter {
        .f-right {
            cursor: pointer;

            i {
                font-size: 14px;
                color: #e3e3e3;
            }
        }

        .product-sidebar__list {
            padding: 0 0.8rem;
            display: none;
        }
    }
}

.product-thumbnails {
    float: none;
    max-width: 100%;
    min-width: 60px;
    position: relative;
    width: 100%;
}

.product-gallery {
    display: flex;
    align-content: flex-start;
    flex-flow: row-reverse nowrap;

    &.product-gallery-horizontal {
        display: block;

        .product-gallery__wrapper {
            max-width: 100%;
            padding-left: 0;
        }

        .slick-arrow {
            left: 84% !important;

            &.slick-prev {
                left: 0;
                top: 18px;
                transform: rotate(-90deg);
            }

            &.slick-next {
                bottom: 32px !important;
                transform: rotate(-90deg);
            }

            @media (min-width: 768px) {
                left: 93% !important;
            }

            &.slick-prev {
                left: 0 !important;
                top: 18px !important;
            }
        }

        .product-thumbnails {
            max-width: 100%;
            padding: 0 40px;
        }
    }

    img {
        max-width: 100%;
        border-radius: 6px;
    }

    .product-gallery__wrapper {
        display: block;
        max-width: calc(100% - 80px);
        padding-left: 10px;
        position: relative;

        .slick-arrow {
            background-color: hsla(0, 0%, 100%, 0.5);
            border-radius: 4px;
            color: #000;
            font-size: 18px;
            height: 35px;
            opacity: 0;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            visibility: hidden;
            width: 35px;
            z-index: 100;

            svg {
                font-size: 10px;
                left: 50%;
                position: absolute;
                top: 50%;
                transform: translate(-50%, -50%);
            }

            &:first-child {
                left: 10px;
            }

            &:last-child {
                right: 10px;
            }

            &:hover {
                background-color: var(--primary-color);
                border-color: var(--primary-color);
                color: #fff;
            }
        }

        &:hover {
            .slick-arrow {
                opacity: 1;
                visibility: visible;
            }
        }
    }

    .product-thumbnails {
        max-width: 80px;
        min-width: 80px;
        width: 100%;

        .slick-arrow {
            position: absolute;
            left: calc(100% / 2 - 17px);
            right: 0;
            background: #fff;
            border: none;
            border-radius: 50%;
            text-align: center;
            vertical-align: middle;
            width: 34px;
            height: 34px;
            line-height: 34px;
            box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
            z-index: 99;
            font-size: 14px;

            &.slick-disabled {
                opacity: 0;
                visibility: hidden;
            }

            &.slick-prev {
                top: -12px;
            }

            &.slick-next {
                bottom: 0;
            }
        }

        .slick-slide.slick-active.slick-current {
            .border {
                border-color: var(--tp-text-primary) !important;
            }
        }

        .slick-slide {
            margin-bottom: 15px;

            img {
                width: 70px !important;
                cursor: pointer;
            }

            &.slick-current {
                img {
                    border: 1px solid var(--tp-text-primary);
                }
            }
        }
    }
}

@media (max-width: 767px) {
    .product-thumbnails {
        padding: 0 40px;
    }

    .product-gallery {
        display: block;

        .product-gallery__wrapper {
            max-width: 100%;
            padding-left: 0;
        }

        .product-thumbnails {
            max-width: 100%;

            .slick-arrow {
                left: 88%;

                &.slick-prev {
                    left: 0;
                    top: 18px;
                    transform: rotate(-90deg);
                }

                &.slick-next {
                    bottom: 32px !important;
                    transform: rotate(-90deg);
                }
            }
        }
    }
}

.product-option {
    .required:after {
        color: #cb4321;
        content: ' *';
    }

    .form-check-input {
        border: none;
        background-color: #ececec;
    }

    .form-check-input:checked {
        background-color: var(--tp-text-primary);
        border-color: transparent;
    }

    .form-check-input:focus {
        outline: 0;
        box-shadow: none;
    }

    .form-check-input[type='checkbox'] {
        border-radius: 0;
        height: 15px;
        width: 15px;
        transform: translateY(2px);
    }

    .form-check-label {
        font-size: 14px;
        color: var(--tp-text-secondary);
    }

    .form-radio {
        // style for type="radio"

        .form-check-input {
            border-radius: 50%;
            height: 15px;
            width: 15px;
            transform: translateY(2px);
        }
    }
}

@media only screen and (max-width: 768px) {
    .bb-ecommerce-table {
        th, td {
            vertical-align: middle;
            text-align: center;
        }


        td {
            display: block;
            width: 100%;
            text-align: right;
        }

        thead {
            display: none;
        }

        td::before {
            content: attr(data-title) " ";
            float: left;
            text-transform: capitalize;
            margin-right: 15px;
            font-weight: bold;
        }
    }
}

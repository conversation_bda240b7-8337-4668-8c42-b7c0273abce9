<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\DataSynchronize\Http\Controllers\ImportController;
use Bo<PERSON>ble\DataSynchronize\Importer\Importer;
use Botble\Ecommerce\Importers\ProductCategoryImporter;

class ImportProductCategoryController extends ImportController
{
    protected function getImporter(): Importer
    {
        return ProductCategoryImporter::make();
    }
}

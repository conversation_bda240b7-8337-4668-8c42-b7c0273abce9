<?php

namespace Bo<PERSON><PERSON>\AuditLog\Providers;

use Bo<PERSON>ble\AuditLog\Commands\ActivityLogClearCommand;
use Bo<PERSON>ble\AuditLog\Commands\CleanOldLogsCommand;
use Botble\Base\Supports\ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            ActivityLogClearCommand::class,
            CleanOldLogsCommand::class,
        ]);
    }
}

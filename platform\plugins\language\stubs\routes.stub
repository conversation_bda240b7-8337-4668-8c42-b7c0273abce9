<?php

/*
|--------------------------------------------------------------------------
| Load The Translated Cached Routes
|--------------------------------------------------------------------------
|
| Here we will decode and unserialize the RouteCollection instance that
| holds all of the route information for an application. This allows
| us to instantaneously load the entire route map into the router.
|
| This also preps LaravelLocalization to deal with translated routes.
|
*/

app('router')->setRoutes(
    unserialize(base64_decode('{{routes}}'))
);

Language::setSerializedTranslatedRoutes('{{translatedRoutes}}');

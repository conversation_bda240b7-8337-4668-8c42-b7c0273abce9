<?php

namespace Bo<PERSON>ble\Contact\Repositories\Eloquent;

use Bo<PERSON>ble\Contact\Enums\ContactStatusEnum;
use Bo<PERSON>ble\Contact\Repositories\Interfaces\ContactInterface;
use <PERSON><PERSON>ble\Support\Repositories\Eloquent\RepositoriesAbstract;
use Illuminate\Database\Eloquent\Collection;

class ContactRepository extends RepositoriesAbstract implements ContactInterface
{
    public function getUnread(array $select = ['*']): Collection
    {
        $data = $this->model
            ->where('status', ContactStatusEnum::UNREAD)
            ->select($select)->latest()
            ->get();

        $this->resetModel();

        return $data;
    }

    public function countUnread(): int
    {
        $data = $this->model->where('status', ContactStatusEnum::UNREAD)->count();
        $this->resetModel();

        return $data;
    }
}

@forward 'spacing';
@forward 'components/theme';
@forward 'components/buttons';
@forward 'components/animation';
@forward 'components/preloader';
@forward 'components/background';
@forward 'components/carousel';
@forward 'components/offcanvas';
@forward 'components/breadcrumb';
@forward 'components/accordion';
@forward 'components/tab';
@forward 'components/modal';
@forward 'components/section-title';
@forward 'layout/header';
@forward 'layout/mobile-menu';
@forward 'layout/slider';
@forward 'layout/category';
@forward 'layout/product';
@forward 'layout/shop';
@forward 'layout/banner';
@forward 'layout/marque';
@forward 'layout/portfolio';
@forward 'layout/blog';
@forward 'layout/cta';
@forward 'layout/services';
@forward 'layout/brand';
@forward 'layout/platinam-product';
@forward 'layout/features';
@forward 'layout/testimonial';
@forward 'layout/selectproduct';
@forward 'layout/product-details';
@forward 'layout/cart';
@forward 'layout/location';
@forward 'layout/sign';
@forward 'layout/coming-soon';
@forward 'layout/footer';

@forward 'ecommerce';
@forward 'custom';

body[dir='rtl'] {
    @import 'rtl';
}

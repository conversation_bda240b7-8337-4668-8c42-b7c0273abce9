.c-content-media-2 {
    padding: 30px;
    position: relative;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.c-content-media-2-slider {
    .c-content-label {
        position: absolute;
        top: 25px;
        left: 40px;
        z-index: 10;
    }

    .owl-controls {
        margin: 0;
        text-align: right;
        position: absolute;
        top: 30px;
        right: 30px;
    }

    &.c-pagination-bottom {
        .owl-controls {
            top: auto;
            bottom: 20px;
            right: 30px;
        }
    }

    .c-content-label {
        position: absolute;
        top: 25px;
        left: 40px;
        z-index: 10;
        font-size: 12px;
        padding: 4px 10px 2px 10px;
        color: #ffffff;
        background-color: #32c5d2;
        font-weight: 600;
        display: inline-block;
    }
}

@media (max-width: 991px) {
    .c-content-media-2 {
        margin-bottom: 20px;
    }

    .c-content-media-2-slider {
        margin-bottom: 20px;
    }
}

.owl-theme {
    .owl-controls {
        margin-top: 10px;
        text-align: center;
    }
}

<?php

namespace Bo<PERSON>ble\Faq\Providers;

use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Faq\Listeners\DeletedContentListener;
use Bo<PERSON>ble\Faq\Listeners\SaveFaqListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UpdatedContentEvent::class => [
            SaveFaqListener::class,
        ],
        CreatedContentEvent::class => [
            SaveFaqListener::class,
        ],
        DeletedContentEvent::class => [
            DeletedContentListener::class,
        ],
    ];
}

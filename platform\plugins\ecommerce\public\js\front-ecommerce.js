(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||c(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function(){return n};var t,n={},a=Object.prototype,o=a.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",s=c.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var a=t&&t.prototype instanceof y?t:y,o=Object.create(a.prototype),c=new P(n||[]);return i(o,"_invoke",{value:_(e,r,c)}),o}function m(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var h="suspendedStart",p="suspendedYield",v="executing",b="completed",g={};function y(){}function w(){}function E(){}var T={};d(T,l,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(O([])));x&&x!==a&&o.call(x,l)&&(T=x);var $=E.prototype=y.prototype=Object.create(T);function S(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function C(t,r){function n(a,i,c,l){var u=m(t[a],t,i);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==e(d)&&o.call(d,"__await")?r.resolve(d.__await).then((function(e){n("next",e,c,l)}),(function(e){n("throw",e,c,l)})):r.resolve(d).then((function(e){s.value=e,c(s)}),(function(e){return n("throw",e,c,l)}))}l(u.arg)}var a;i(this,"_invoke",{value:function(e,t){function o(){return new r((function(r,a){n(e,t,r,a)}))}return a=a?a.then(o,o):o()}})}function _(e,r,n){var a=h;return function(o,i){if(a===v)throw Error("Generator is already running");if(a===b){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var c=n.delegate;if(c){var l=j(c,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=v;var u=m(e,r,n);if("normal"===u.type){if(a=n.done?b:p,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=b,n.method="throw",n.arg=u.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=m(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function O(r){if(r||""===r){var n=r[l];if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var a=-1,i=function e(){for(;++a<r.length;)if(o.call(r,a))return e.value=r[a],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(e(r)+" is not iterable")}return w.prototype=E,i($,"constructor",{value:E,configurable:!0}),i(E,"constructor",{value:w,configurable:!0}),w.displayName=d(E,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,E):(e.__proto__=E,d(e,s,"GeneratorFunction")),e.prototype=Object.create($),e},n.awrap=function(e){return{__await:e}},S(C.prototype),d(C.prototype,u,(function(){return this})),n.AsyncIterator=C,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var i=new C(f(e,t,r,a),o);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S($),d($,s,"Generator"),d($,l,(function(){return this})),d($,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=O,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,a){return c.type="throw",c.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var l=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),A(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;A(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:O(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},n}function n(e,t,r,n,a,o,i){try{var c=e[o](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function a(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var i=e.apply(t,r);function c(e){n(i,a,o,c,l,"next",e)}function l(e){n(i,a,o,c,l,"throw",e)}c(void 0)}))}}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,c=[],l=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=o.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){u=!0,a=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return c}}(e,t)||c(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=c(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,o=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw o}}}}function c(e,t){if(e){if("string"==typeof e)return l(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function u(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,f(n.key),n)}}function s(e,t,r){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,r)}function d(e,t,r){return(t=f(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,r||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==e(r)?r:r+""}function m(e,t){return e.get(function(e,t,r){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:r;throw new TypeError("Private element is not present on this object")}(e,t))}var h=new WeakMap,p=new WeakMap,v=new WeakMap,b=new WeakMap,g=function(){return e=function e(){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),d(this,"quickSearchAjax",null),d(this,"filterAjax",null),d(this,"lastFilterFormData",null),d(this,"lastFilterFormAction",null),d(this,"filterTimeout",null),s(this,h,(function(e){var t=[],r={},n={};return e.forEach((function(e){if(e.value)if(e.name.endsWith("[]")){var a=e.name.slice(0,-2);r[a]||(r[a]=new Set),r[a].add(e.value)}else n[e.name]||(n[e.name]=!0,t.push(e))})),Object.keys(r).forEach((function(e){var n=Array.from(r[e]);n.length>0&&t.push({name:e,value:n.join(",")})})),t})),d(this,"highlightSearchKeywords",(function(e,t){if(t.trim()){var r=t.trim().split(/\s+/),n=new RegExp("(".concat(r.join("|"),")"),"gi");e.html(e.text().replace(n,'<span class="bb-quick-search-highlight">$1</span>'))}})),s(this,p,(function(e,t){var r=e.find('button[type="submit"]'),a=e.find('input[name="q"]'),o=e.find(".bb-quick-search-results");a.val()?n.quickSearchAjax=$.ajax({type:"GET",url:t||e.data("ajax-url"),data:e.serialize(),beforeSend:function(){r.addClass("btn-loading"),t||o.removeClass("show").html(""),null!==n.quickSearchAjax&&n.quickSearchAjax.abort()},success:function(e){var r=e.error,i=e.message,c=e.data;if(r)Theme.showError(i);else{o.addClass("show"),t?o.find(".bb-quick-search-list").append($(c).find(".bb-quick-search-list").html()):o.html(c);var l=n,u=a.val();o.find(".bb-quick-search-item-name").each((function(){$(this).html($(this).text()),u&&l.highlightSearchKeywords($(this),u)})),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update()}},complete:function(){return r.removeClass("btn-loading")}}):o.removeClass("show").html("")})),s(this,v,(function(e,t,r){var a=$(".bb-product-form-filter");e&&!t&&(n.lastFilterFormData=null,n.lastFilterFormAction=null);var c=t;if(Array.isArray(t)){var l=new URLSearchParams,u={};t.forEach((function(e){e.name&&e.value&&(u[e.name]=e.value)})),Object.keys(u).forEach((function(e){l.set(e,u[e])})),c={};var s,d=i(l.entries());try{for(d.s();!(s=d.n()).done;){var f=o(s.value,2),m=f[0],h=f[1];c[m]=h}}catch(e){d.e(e)}finally{d.f()}}n.filterAjax=$.ajax({url:e,type:"GET",data:c,beforeSend:function(){document.dispatchEvent(new CustomEvent("ecommerce.product-filter.before",{detail:{data:c,element:a}}))},success:function(t){var c=t.message;if(t.error)return Theme.showError(c),void(n.filterAjax=null);var l=r||e;if(l.includes("?")){var u,s=l.split("?"),d=s[0],f=new URLSearchParams(s[1]),m=new URLSearchParams,h=i(f.entries());try{for(h.s();!(u=h.n()).done;){var p=o(u.value,2),v=p[0],b=p[1];m.has(v)||m.set(v,b)}}catch(e){h.e(e)}finally{h.f()}l=d+"?"+m.toString()}window.history.pushState(t,null,l),document.dispatchEvent(new CustomEvent("ecommerce.product-filter.success",{detail:{data:t,element:a}})),n.filterAjax=null,$(".bb-product-price-filter").length&&EcommerceApp.initPriceFilter()},error:function(e){"abort"!==e.statusText&&(console.error("Filter request failed:",e),Theme.handleError(e))},complete:function(){n.filterTimeout=null,n.filterAjax=null,void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update(),document.dispatchEvent(new CustomEvent("ecommerce.product-filter.completed",{detail:{element:a}}))}})})),s(this,b,a(r().mark((function e(){var t,n,a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=function(e,t,r){t(),fetch(e,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"}}).then((function(e){if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((function(e){var t=e.error,n=e.data;t||(r(n),document.dispatchEvent(new CustomEvent("ecommerce.categories-dropdown.success",{detail:{data:n}})))})).catch((function(e){Theme.handleError(e)}))},(n=$(document).find('[data-bb-toggle="init-categories-dropdown"]')).length&&(a=n.first().data("url"),t(a,(function(){}),(function(e){n.each((function(t,r){var n=$(r),a=$(n.data("bb-target"));a.length?a.html(e.dropdown):n.append(e.select),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update()}))})));case 3:case"end":return e.stop()}}),e)})))),d(this,"productQuantityToggle",(function(){$('[data-bb-toggle="product-quantity"]').on("click",'[data-bb-toggle="product-quantity-toggle"]',(function(e){var t=$(e.currentTarget),r=t.data("value");if(r){var n=null;if("plus"===r?n=t.prev():"minus"===r&&(n=t.next()),n){var a=parseInt(n.val())||1;n.val("plus"===r?a+1:1===a?1:a-1),document.dispatchEvent(new CustomEvent("ecommerce.cart.quantity.change",{detail:{element:t,action:"+"===r?"increase":"decrease"}}))}}}))})),d(this,"onChangeProductAttribute",(function(){window.onBeforeChangeSwatches&&"function"==typeof window.onBeforeChangeSwatches||(window.onBeforeChangeSwatches=function(e,t){var r=t.closest("form");e&&(r.find('button[type="submit"]').prop("disabled",!0),r.find('button[data-bb-toggle="add-to-cart"]').prop("disabled",!0))}),window.onChangeSwatchesSuccess&&"function"==typeof window.onChangeSwatchesSuccess||(window.onChangeSwatchesSuccess=function(e,t){if(e){var r=$(".bb-product-detail"),n=t.closest("form"),a=n.find('button[type="submit"]'),o=n.find('input[name="qty"]'),i=r.find(".number-items-available"),c=r.find('[data-bb-value="product-sku"]'),l=e.error,u=e.data;if(l)return a.prop("disabled",!0),o.prop("disabled",!0),void n.find('input[name="id"]').val("");a.prop("disabled",!1),o.prop("disabled",!1),n.find('input[name="id"]').val(u.id),r.find('[data-bb-value="product-price"]').text(u.display_sale_price),u.sale_price!==u.price?r.find('[data-bb-value="product-original-price"]').text(u.display_price).show():r.find('[data-bb-value="product-original-price"]').hide(),u.sku?(c.text(u.sku),c.closest("div").show()):c.closest("div").hide(),u.error_message?(a.prop("disabled",!0),o.prop("disabled",!0),i.html("<span class='text-danger'>".concat(u.error_message,"</span>")).show()):u.warning_message?i.html("<span class='text-warning fw-medium fs-6'>".concat(u.warning_message,"</span>")).show():u.success_message?i.html("<span class='text-success'>".concat(u.success_message,"</span>")).show():i.html("").hide(),r.find(".bb-product-attribute-swatch-item").removeClass("disabled"),r.find(".bb-product-attribute-swatch-list select option").prop("disabled",!1);var s=u.unavailable_attribute_ids||[];s.length&&s.map((function(e){var t=r.find('.bb-product-attribute-swatch-item[data-id="'.concat(e,'"]'));t.length?(t.addClass("disabled"),t.find("input").prop("checked",!1)):(t=r.find('.bb-product-attribute-swatch-list select option[data-id="'.concat(e,'"]'))).length&&t.prop("disabled",!0)}));var d="",f="",m=window.siteConfig||{};u.image_with_sizes.origin.length?u.image_with_sizes.origin.forEach((function(e){d+="\n                    <a href='".concat(e,"'>\n                        <img src='").concat(e,"' alt='").concat(u.name,"'>\n                    </a>\n                ")})):u.image_with_sizes.origin.push(m.img_placeholder),u.image_with_sizes.thumb.length?u.image_with_sizes.thumb.forEach((function(e){f+="\n                    <div>\n                        <img src='".concat(e,"' alt='").concat(u.name,"'>\n                    </div>\n                ")})):u.image_with_sizes.thumb.push(m.img_placeholder);var h=r.find(".bb-product-gallery");h.find(".bb-product-gallery-thumbnails").slick("unslick").html(f);var p=$(document).find(".bb-quick-view-gallery-images");p.length&&p.slick("unslick").html(d),h.find(".bb-product-gallery-images").slick("unslick").html(d),"undefined"!=typeof EcommerceApp&&EcommerceApp.initProductGallery()}})})),d(this,"handleUpdateCart",(function(e){var t;t=e?$(e).closest("form"):$("form.cart-form"),$.ajax({type:"POST",url:t.prop("action"),data:t.serialize(),success:function(e){var t=e.error,r=e.message,a=e.data;t&&Theme.showError(r),n.ajaxLoadCart(a)},error:function(e){return Theme.handleError(e)}})})),d(this,"ajaxLoadCart",(function(e){if(e){var t=$('[data-bb-toggle="cart-content"]');void 0!==e.count&&$('[data-bb-value="cart-count"]').text(e.count),void 0!==e.total_price&&$('[data-bb-value="cart-total-price"]').text(e.total_price),t.length&&(t.replaceWith(e.cart_content),n.productQuantityToggle(),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update())}})),$(document).on("click",'[data-bb-toggle="toggle-product-categories-tree"]',(function(e){e.preventDefault();var t=$(e.currentTarget);t.toggleClass("active"),t.closest(".bb-product-filter-item").find("> .bb-product-filter-items").slideToggle().toggleClass("active")})).on("click",'[data-bb-toggle="toggle-filter-sidebar"]',(function(){$(".bb-filter-offcanvas-area").toggleClass("offcanvas-opened"),$(".body-overlay").toggleClass("opened")})).on("click",".body-overlay",(function(){$(".bb-filter-offcanvas-area").removeClass("offcanvas-opened"),$(".body-overlay").removeClass("opened")})).on("submit","form.bb-product-form-filter",(function(e){e.preventDefault();var r=$(e.currentTarget),a=m(h,n).call(n,r.serializeArray()),o=r.prop("action"),i=o,c=new URLSearchParams,l={};a.forEach((function(e){l[e.name]||(l[e.name]=e.value)})),Object.keys(l).forEach((function(e){l[e]&&c.set(e,l[e])}));var u=c.toString();u&&(i+="?".concat(u));var s=[].concat(t(a),[{name:"_",value:Date.now()}]);if(window.location.href!==i){var d=JSON.stringify(a),f=r.prop("action");n.lastFilterFormData===d&&n.lastFilterFormAction===f||(n.lastFilterFormData=d,n.lastFilterFormAction=f,n.filterAjax&&n.filterAjax.abort(),n.filterTimeout&&clearTimeout(n.filterTimeout),n.filterTimeout=setTimeout((function(){m(v,n).call(n,o,s,i)}),100))}})).on("change","form.bb-product-form-filter input, form.bb-product-form-filter select",(function(e){var t=$(e.currentTarget),r=t.closest("form");if("checkbox"===t.attr("type")&&t.attr("name")&&t.attr("name").endsWith("[]")){var n=t.attr("name").slice(0,-2),a=r.find('input[name="'.concat(t.attr("name"),'"]')).filter(":checked"),o=r.find('input[name="'.concat(n,'"]'));o.length||(r.append('<input type="hidden" name="'.concat(n,'" value="">')),o=r.find('input[name="'.concat(n,'"]')));var i=a.map((function(){return $(this).val()})).get();o.val(i.join(","))}r.trigger("submit")})).on("keyup",".bb-form-quick-search input",(function(e){m(p,n).call(n,$(e.currentTarget).closest("form"))})).on("click","body",(function(e){$(e.target).closest(".bb-form-quick-search").length||$(".bb-quick-search-results").removeClass("show").html("")})).on("click",'[data-bb-toggle="quick-shop"]',(function(e){var t=$(e.currentTarget),r=$("#quick-shop-modal");$.ajax({url:t.data("url"),type:"GET",beforeSend:function(){r.find(".modal-body").html(""),r.modal("show"),document.dispatchEvent(new CustomEvent("ecommerce.quick-shop.before-send",{detail:{element:t,modal:r}}))},success:function(e){var t=e.data;r.find(".modal-body").html(t)},error:function(e){return Theme.handleError(e)},complete:function(){document.dispatchEvent(new CustomEvent("ecommerce.quick-shop.completed",{detail:{element:t,modal:r}}))}})})).on("click",".bb-product-filter-link",(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.closest("form"),n=t.closest(".bb-product-filter"),a=t.data("id"),o=r.find('input[name="categories"]');o.length||(o=r.find('input[name="categories[]"]')),n.find(".bb-product-filter-link").removeClass("active"),t.addClass("active"),r.find('input[name="page"]').remove(),r.find('input[name="per-page"]').remove(),o.length&&a?(o.attr("name"),o.val(a).trigger("change")):(a||o.length&&o.val(null),r.prop("action",t.prop("href")).trigger("submit"))})).on("click",".bb-product-filter-clear",(function(e){e.preventDefault();var t=$(e.currentTarget);m(v,n).call(n,t.prop("href"))})).on("click",".bb-product-filter-clear-all",(function(e){e.preventDefault();var t=$(".bb-product-form-filter");t.find('input[type="text"], input[type="hidden"], input[type="radio"], select').val(null),t.find('input[type="checkbox"]').prop("checked",!1),t.trigger("submit")})).on("submit","form#cancel-order-form",(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.closest(".modal"),n=r.find('button[type="submit"]');$.ajax({url:t.prop("action"),type:"POST",data:t.serialize(),beforeSend:function(){n.addClass("btn-loading")},success:function(e){var t=e.error,n=e.message;t?Theme.showError(n):(Theme.showSuccess(n),r.modal("hide"),setTimeout((function(){return window.location.reload()}),1e3))},error:function(e){return Theme.handleError(e)},complete:function(){return n.removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="add-to-compare"]',(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.hasClass("active")?t.data("remove-url"):t.data("url"),n={};t.hasClass("active")&&(n={_method:"DELETE"}),$.ajax({url:r,method:"POST",data:n,beforeSend:function(){return t.addClass("btn-loading")},success:function(e){var r=e.error,n=e.message,a=e.data;r?Theme.showError(n):(Theme.showSuccess(n),t.toggleClass("active"),void 0!==a.count&&$('[data-bb-value="compare-count"]').text(a.count),t.hasClass("active")?document.dispatchEvent(new CustomEvent("ecommerce.compare.added",{detail:{data:a,element:t}})):document.dispatchEvent(new CustomEvent("ecommerce.compare.removed",{detail:{data:a,element:t}})))},error:function(e){return Theme.handleError(e)},complete:function(){return t.removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="remove-from-compare"]',(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.closest("table");$.ajax({url:t.data("url"),method:"POST",data:{_method:"DELETE"},success:function(e){var n=e.error,a=e.message,o=e.data;n?Theme.showError(a):(Theme.showSuccess(a),document.dispatchEvent(new CustomEvent("ecommerce.compare.removed",{detail:{data:o,element:t}})),void 0!==o.count&&$('[data-bb-value="compare-count"]').text(o.count),o.count>0?r.find("td:nth-child(".concat(t.closest("td").index()+1,")")).remove():window.location.reload())},error:function(e){return Theme.handleError(e)}})})).on("click",'[data-bb-toggle="add-to-wishlist"]',(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.data("url");$.ajax({url:r,method:"POST",beforeSend:function(){return t.addClass("btn-loading")},success:function(e){var r=e.error,n=e.message,a=e.data;r?Theme.showError(n):(void 0!==a.count&&$('[data-bb-value="wishlist-count"]').text(a.count),Theme.showSuccess(n),document.dispatchEvent(new CustomEvent("ecommerce.wishlist.added",{detail:{data:a,element:t}})))},error:function(e){return Theme.handleError(e)},complete:function(){return t.removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="remove-from-wishlist"]',(function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),method:"POST",data:{_method:"DELETE"},beforeSend:function(){return t.addClass("btn-loading")},success:function(e){var r=e.error,n=e.message,a=e.data;r?Theme.showError(n):(Theme.showSuccess(n),void 0!==a.count&&$('[data-bb-value="wishlist-count"]').text(a.count),t.closest("tr").remove(),0===a.count&&window.location.reload(),document.dispatchEvent(new CustomEvent("ecommerce.wishlist.removed",{detail:{data:a,element:t}})))},error:function(e){return Theme.handleError(e)},complete:function(){return t.removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="add-to-cart"]',(function(e){e.preventDefault();var t=$(e.currentTarget),r={id:t.data("id")},n=t.closest("tr").find('input[name="qty"]');n&&(r.qty=n.val()),$.ajax({url:t.data("url"),method:"POST",data:r,dataType:"json",beforeSend:function(){return t.addClass("btn-loading")},success:function(e){var r=e.error,n=e.message,a=e.data;if(r)return Theme.showError(n),void 0!==a.next_url&&setTimeout((function(){window.location.href=a.next_url}),500),!1;if(a&&void 0!==a.next_url)return window.location.href=a.next_url,!1;var o=!0;void 0!==t.data("show-toast-on-success")&&(o=t.data("show-toast-on-success")),o&&Theme.showSuccess(n),void 0!==a.count&&$('[data-bb-value="cart-count"]').text(a.count),document.dispatchEvent(new CustomEvent("ecommerce.cart.added",{detail:{data:a,element:t}}))},error:function(e){return Theme.handleError(e)},complete:function(){return t.removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="remove-from-cart"]',(function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.prop("href")||t.data("url"),method:"GET",beforeSend:function(){return t.addClass("btn-loading")},success:function(e){var r=e.error,n=e.message,a=e.data;r?Theme.showError(n):(Theme.showSuccess(n),t.closest("tr").remove(),void 0!==a.count&&$('[data-bb-value="cart-count"]').text(a.count),0===a.count&&window.location.reload(),document.dispatchEvent(new CustomEvent("ecommerce.cart.removed",{detail:{data:a,element:t}})))},error:function(e){return Theme.handleError(e)},complete:function(){return t.removeClass("btn-loading")}})})).on("submit",'[data-bb-toggle="coupon-form"]',(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.find('button[type="submit"]');$.ajax({url:t.prop("action"),type:"POST",data:t.serialize(),beforeSend:function(){return r.prop("disabled",!0).addClass("btn-loading")},success:function(e){var r=e.error,n=e.message,a=e.data;r?Theme.showError(n):(Theme.showSuccess(n),document.dispatchEvent(new CustomEvent("ecommerce.coupon.applied",{detail:{data:a,element:t}})))},error:function(e){return Theme.handleError(e)},complete:function(){return r.prop("disabled",!1).removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="quick-view-product"]',(function(e){e.preventDefault();var t=$(e.currentTarget);$.ajax({url:t.data("url"),type:"GET",beforeSend:function(){return t.prop("disabled",!0).addClass("btn-loading")},success:function(e){var r=e.error,a=e.message,o=e.data;if(r)Theme.showError(a);else{var i=$('[data-bb-toggle="quick-view-modal"]');i.modal("show"),i.find(".modal-body").html(o),document.dispatchEvent(new CustomEvent("ecommerce.quick-view.initialized",{detail:{data:o,element:t}})),setTimeout((function(){n.initProductGallery(!0)}),100)}},error:function(e){return Theme.handleError(e)},complete:function(){return t.prop("disabled",!1).removeClass("btn-loading")}})})).on("click",'[data-bb-toggle="product-form"] button[type="submit"]',(function(e){e.preventDefault();var t=$(e.currentTarget),r=t.closest("form"),n=r.serializeArray();""!==r.find('input[name="id"]').val()&&(n.push({name:"checkout",value:"checkout"===t.prop("name")?1:0}),$.ajax({type:"POST",url:r.prop("action"),data:n,beforeSend:function(){t.prop("disabled",!0).addClass("btn-loading")},success:function(e){var n=e.error,a=e.message,o=e.data;n?Theme.showError(a):(Theme.showSuccess(a),r.find('input[name="qty"]').val(1),void 0!==o.count&&$('[data-bb-value="cart-count"]').text(o.count),document.dispatchEvent(new CustomEvent("ecommerce.cart.added",{detail:{data:o,element:t}})))},error:function(e){return Theme.handleError(e)},complete:function(){return t.prop("disabled",!1).removeClass("btn-loading")}}))})).on("change",'[data-bb-toggle="product-form-filter-item"]',(function(e){var t=$(e.currentTarget),r=$(".bb-product-form-filter"),n=t.prop("name"),a=t.val();if(n.endsWith("[]")){var o=n.slice(0,-2),i=r.find('input[name="'.concat(o,'"]'));i.length||(r.append('<input type="hidden" name="'.concat(o,'" value="">')),i=r.find('input[name="'.concat(o,'"]')));var c=i.val()?i.val().split(","):[],l=new Set(c);l.add(a),i.val(Array.from(l).join(","))}else{var u=r.find('input[name="'.concat(n,'"]'));u.length&&u.val(a)}r.trigger("submit")})),$(".bb-product-price-filter").length&&this.initPriceFilter(),m(b,this).call(this)},n=[{key:"isRtl",value:function(){return"rtl"===document.body.getAttribute("dir")}},{key:"initLightGallery",value:function(e){e.length&&(e.data("lightGallery")&&e.data("lightGallery").destroy(!0),e.lightGallery({selector:"a",thumbnail:!0,share:!1,fullScreen:!1,autoplay:!1,autoplayControls:!1,actualSize:!1}))}},{key:"initProductGallery",value:function(){var e=this;if(!(arguments.length>0&&void 0!==arguments[0]&&arguments[0])){var t=function(e,t){null!=e&&null!=t&&e.contentWindow.postMessage(JSON.stringify(t),"*")},r=function(e,r){var n,a,o,i,c;if(a=(n=e.find(".slick-current")).data("provider"),i=n.get(0),o=n.data("video-start"),"vimeo"===a)switch(r){case"play":null!=o&&o>0&&!n.hasClass("started")&&(n.addClass("started"),t(i,{method:"setCurrentTime",value:o})),t(i,{method:"play",value:1});break;case"pause":t(i,{method:"pause",value:1})}else if("youtube"===a)switch(r){case"play":t(i,{event:"command",func:"mute"}),t(i,{event:"command",func:"playVideo"});break;case"pause":t(i,{event:"command",func:"pauseVideo"})}else"video"===a&&null!=(c=n.children("video").get(0))&&("play"===r?c.play():c.pause())},n=$(document).find(".bb-product-gallery-images");if(!n.length)return;var a=$(document).find(".bb-product-gallery-thumbnails");n.on("init",(function(e){e=$(e.currentTarget),setTimeout((function(){r(e,"play")}),1e3)})),n.on("beforeChange",(function(e,t){t=$(t.$slider),r(t,"pause")})),n.on("afterChange",(function(e,t){t=$(t.$slider),r(t,"play")})),$(document).on("click",".bb-button-trigger-play-video",(function(e){var t=$(e.currentTarget),r=document.getElementById(t.data("target"));r.play(),t.closest(".bb-product-video").addClass("video-playing"),r.addEventListener("ended",(function(){t.closest(".bb-product-video").removeClass("video-playing"),r.currentTime=0,r.pause()}))})),n.length&&n.map((function(t,r){var n=$(r);n.hasClass("slick-initialized")&&n.slick("unslick"),n.slick({slidesToShow:1,slidesToScroll:1,arrows:!1,dots:!1,infinite:!1,fade:!0,lazyLoad:"ondemand",asNavFor:".bb-product-gallery-thumbnails",rtl:e.isRtl()})})),a.length&&a.slick({slidesToShow:6,slidesToScroll:1,asNavFor:".bb-product-gallery-images",focusOnSelect:!0,infinite:!1,rtl:this.isRtl(),vertical:1===a.data("vertical"),prevArrow:'<button class="slick-prev slick-arrow"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M15 6l-6 6l6 6" /></svg></button>',nextArrow:'<button class="slick-next slick-arrow"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 6l6 6l-6 6" /></svg></button>',responsive:[{breakpoint:768,settings:{slidesToShow:4,vertical:!1}}]}),this.initLightGallery(n),void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update()}var o=$(document).find(".bb-quick-view-gallery-images");o.length&&(o.hasClass("slick-initialized")&&o.slick("unslick"),o.slick({slidesToShow:1,slidesToScroll:1,dots:!1,arrows:!0,adaptiveHeight:!1,rtl:this.isRtl()})),this.initLightGallery(o)}},{key:"initPriceFilter",value:function(){if(void 0===$.fn.slider)throw new Error("jQuery UI slider is required for price filter");var e=$(document).find(".bb-product-price-filter"),t=e.find(".price-slider"),r=e.find(".input-range-label");if(e){var n=e.find('input[name="min_price"]'),a=e.find('input[name="max_price"]'),o=n.val()||t.data("min"),i=a.val()||t.data("max");t.slider({range:!0,min:t.data("min"),max:t.data("max"),values:[o,i],slide:function(e,t){r.find(".from").text(EcommerceApp.formatPrice(t.values[0])),r.find(".to").text(EcommerceApp.formatPrice(t.values[1]))},change:function(e,t){parseInt(o)!==t.values[0]&&n.val(t.values[0]).trigger("change"),parseInt(i)!==t.values[1]&&a.val(t.values[1]).trigger("change")}}),r.find(".from").text(this.formatPrice(t.slider("values",0))),r.find(".to").text(this.formatPrice(t.slider("values",1)))}}},{key:"formatPrice",value:function(e,t,r){var n=window.currencies||{};t||(t=void 0!==n.number_after_dot?n.number_after_dot:2);var a="\\d(?=(\\d{"+(r||3)+"})+$)",o="";if(n.show_symbol_or_title&&(o=n.symbol||n.title||""),n.display_big_money){var i="";e>=1e6&&e<1e9?(e/=1e6,i=n.million):e>=1e9&&(e/=1e9,i=n.billion),o=i+(o?" ".concat(o):"")}return e=(e=e.toFixed(Math.max(0,~~t)).toString().split("."))[0].toString().replace(new RegExp(a,"g"),"$&".concat(n.thousands_separator))+(e[1]?n.decimal_separator+e[1]:""),n.show_symbol_or_title&&(e=n.is_prefix_symbol?o+e:e+o),e}}],n&&u(e.prototype,n),c&&u(e,c),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,c}();$((function(){window.EcommerceApp=new g,EcommerceApp.productQuantityToggle(),EcommerceApp.initProductGallery(),EcommerceApp.onChangeProductAttribute(),$(".bb-product-price-filter").length&&EcommerceApp.initPriceFilter(),document.addEventListener("ecommerce.quick-shop.completed",(function(){EcommerceApp.productQuantityToggle()})),document.addEventListener("ecommerce.cart.quantity.change",(function(e){var t=e.detail.element;EcommerceApp.handleUpdateCart(t)})),document.addEventListener("ecommerce.product-filter.before",(function(){var e=$('[data-bb-toggle="product-list"]').find(".bb-product-items-wrapper");e.length&&e.append('<div class="loading-spinner"></div>')})),document.addEventListener("ecommerce.product-filter.success",(function(e){var t=e.detail.data,r=$(".bb-product-items-wrapper");if(r.length&&r.html(t.data),t.additional){var n=$(".bb-shop-sidebar"),a=$("[data-bb-filter-sidebar]");a.length||(a=n);var o={};$(".bb-product-filter-link.active").each((function(){var e=$(this).closest(".bb-product-filter").data("filter-group");e&&(o[e]=$(this).data("id"))})),a.replaceWith(t.additional.filters_html),Object.keys(o).forEach((function(e){var t=o[e];$('.bb-product-filter[data-filter-group="'.concat(e,'"] .bb-product-filter-link[data-id="').concat(t,'"]')).addClass("active")}))}$(document).find(".bb-product-price-filter").length&&EcommerceApp.initPriceFilter(),r.length&&$("html, body").animate({scrollTop:r.offset().top-120});var i=$('[data-bb-toggle="product-list"]').find(".bb-product-items-wrapper");i.length&&i.find(".loading-spinner").remove()})),document.addEventListener("ecommerce.product-filter.completed",(function(){void 0!==Theme.lazyLoadInstance&&Theme.lazyLoadInstance.update()}))}))})();
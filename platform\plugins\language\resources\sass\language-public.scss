.language-wrapper {
    .dropdown {
        height: 100%;

        &.open {
            .language_bar_chooser {
                &.dropdown-menu {
                    display: block;
                }
            }
        }

        .btn {
            border: none !important;
            background: none !important;
            margin-bottom: 0;
            border-radius: 0 !important;
            padding: 7px 15px;
            outline: none !important;
            -webkit-box-shadow: none !important;
            -moz-box-shadow: none !important;
            box-shadow: none !important;
            -webkit-transition: all 0.4s ease;
            -moz-transition: all 0.4s ease;
            transition: all 0.4s ease;
            color: inherit !important;
            font-size: inherit;
            height: 100%;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1000;
            display: none;
            float: left;
            min-width: 160px;
            font-size: 14px;
            text-align: left;
            list-style: none;
            background-color: #ffffff;
            -webkit-background-clip: padding-box;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
            width: 160px;
            line-height: 58px;
            margin: 0;
            padding: 0;

            li {
                .flag {
                    margin: 0 10px;
                }

                span {
                    color: #222222;
                }

                &.active {
                    a {
                        span {
                            color: #fff;
                        }
                    }
                }
            }
        }

        .language_bar_chooser {
            display: block;
            float: right;
            width: 160px;
            line-height: 58px;
            text-align: center;

            li {
                display: inline-block;
                float: left;
                margin-left: 5px;
            }

            &.dropdown-menu {
                display: none;
                border-radius: 0 !important;

                li {
                    width: 100%;
                    margin: 0;
                    display: block;

                    &:hover {
                        background: #dddddd;
                    }

                    a {
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        margin-right: 0 !important;
                        width: 100%;
                        text-align: left;
                    }
                }
            }
        }
    }

    .language_bar_list {
        li {
            display: inline-block;
            margin-left: 5px;
            min-width: 90px;

            a {
                width: 100%;
                align-items: center;
                display: flex;
                justify-content: center;

                .flag {
                    margin-right: 10px;
                    width: 20px;
                }

                span {
                    color: #222222;
                }
            }
        }
    }
}

body[dir='rtl'] {
    .language-wrapper {
        .language_bar_list {
            li {
                a {
                    .flag {
                        margin-right: 0;
                        margin-left: 10px;
                    }
                }
            }
        }

        .language_bar_chooser.dropdown-menu {
            li {
                a {
                    text-align: right;
                }
            }
        }
    }
}

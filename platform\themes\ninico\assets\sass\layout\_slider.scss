@use '../utils' as *;

.tp-slide-item {
    position: relative;
    @media #{$xs} {
        margin-bottom: 20px;
    }

    &__img {
        & img {
            border-radius: 6px;
            max-width: 100%;
        }
    }

    &__content {
        position: absolute;
        z-index: 1;
        top: 50%;
        transform: translateY(-50%);
        inset-inline-start: 0;
        padding-inline-start: 60px;
        max-width: 60%;
        @media #{$xl} {
            padding-inline-start: 35px;
            max-width: 70%;
        }
        @media #{$lg} {
            padding-inline-start: 30px;
            max-width: 70%;
        }
        @media #{$md} {
            padding-inline-start: 35px;
        }
        @media #{$xs} {
            padding-inline-start: 15px;
            max-width: 80%;
        }
        @media #{$sm} {
            padding-inline-start: 15px;
            max-width: 50%;
        }
    }

    &__sub-title {
        font-size: 20px;
        font-weight: 400;
        color: var(--tp-text-primary);
        display: block;
        margin-bottom: 5px;
        @media #{$xs} {
            font-size: 16px;
        }
    }

    &__title {
        color: var(--tp-text-body);
        font-size: 50px;
        font-weight: 600;
        line-height: 1.2;
        @media #{$xs} {
            font-size: 18px;
            margin-bottom: 10px;
        }
        @media #{$sm} {
            font-size: 26px;
        }

        & i {
            font-style: normal;
            color: var(--tp-text-primary);
            position: relative;

            & img {
                position: absolute;
                inset-inline-start: -6px;
                bottom: 10px;
                z-index: -1;
                animation: section-animation 3s infinite;
            }
        }
    }

    &__thumb {
        & img {
            @media #{$xxl} {
                max-width: 100%;
            }
        }
    }
}

.tp-slider-area {
    & .slider-pagination {
        position: absolute;
        bottom: 15px;
        z-index: 9;
        inset-inline-start: 0;
        margin: 0 auto;
        inset-inline-end: 0;
        text-align: center;
        @media #{$xs} {
            margin-bottom: 10px;
        }

        & .swiper-pagination-bullet {
            width: 25px;
            height: 4px;
            display: inline-block;
            border-radius: 5px;
            background: var(--tp-common-black);
            margin: 0 5px;
            opacity: 0.1;
        }

        & .swiper-pagination-bullet-active {
            background: var(--tp-text-primary);
            opacity: 1;
        }
    }
}

.swiper-slide-active {
    & .tp-slide-item__sub-title,
    & .tp-slide-item__title,
    & .tp-slide-item__slide-btn,
    & .tpslidertwo__sub-title,
    & .tpslidertwo__title,
    & .tpslidertwo__content p,
    & .tpslidertwo__slide-btn {
        animation-fill-mode: both;
        animation-name: fadeInUp;
    }
}

.swiper-slide-active {
    & .tpslidertwo__img {
        animation-fill-mode: both;
        animation-name: fadeInRight;
    }
}

.swiper-slide-active .tp-slide-item__sub-title,
.swiper-slide-active .tpslidertwo__sub-title {
    animation-delay: 0.6s;
    animation-duration: 0.8s;
}

.swiper-slide-active .tp-slide-item__title,
.swiper-slide-active .tpslidertwo__title {
    animation-delay: 0.8s;
    animation-duration: 1s;
}

.swiper-slide-active .tp-slide-item__slide-btn {
    animation-delay: 1s;
    animation-duration: 1.2s;
}

.swiper-slide-active .tpslidertwo__content p {
    animation-delay: 1s;
    animation-duration: 1.2s;
}

.swiper-slide-active .tpslidertwo__slide-btn {
    animation-delay: 1.4s;
    animation-duration: 1.3s;
}

.swiper-slide-active .tpslidertwo__img {
    animation-delay: 1s;
    animation-duration: 1s;
}

.tpslidertwo {
    &__item {
        margin-top: -70px;
        @media #{$xs} {
            margin-top: 0;
            margin-inline-start: 0;
        }
    }

    &__img {
        & > img {
            @media #{$md} {
                width: 360px;
            }
        }

        &-shape {
            & img {
                @media #{$md} {
                    height: 100px;
                    width: 100px;
                }
            }
        }
    }
}

.tpslider-banner {
    &:hover {
        & .tpslider-banner__img {
            border-radius: 6px;

            & img {
                transform: scale(1.05);
                border-radius: 6px;
            }
        }
    }

    @media #{$xl} {
        margin-bottom: 20px;
    }

    &__content {
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        padding: 30px 10px 10px 30px;
        @media #{$xl} {
            padding: 15px 10px 10px 20px;
        }
        @media #{$lg} {
            padding: 15px 10px 10px 15px;
        }
    }

    &__sub-title {
        font-size: 16px;
        font-weight: 400;
        color: var(--tp-text-primary);
    }

    &__title {
        font-size: 20px;
        font-weight: 600;
        color: var(--tp-text-body);

        & br {
            @media #{$sm} {
                display: none;
            }
        }

        @media #{$lg} {
            font-size: 16px;
        }
    }

    &__img {
        position: relative;
        width: 100%;
        border-radius: 6px;
        @include transition(0.3s);
        overflow: hidden;
        @media #{$md} {
            margin-top: 20px;
            margin-inline-start: 0;
        }
        @media #{$xs} {
            margin-inline-start: 0;
        }

        & img {
            border-radius: 6px;
            width: 100%;
            @media #{$xl} {
                height: 204px;
                object-fit: cover;
            }
            @media #{$lg} {
                height: 170px;
                object-fit: cover;
            }
            @media #{$sm} {
                width: 100%;
            }
        }
    }
}

.tpslider-banner__img.tpbannerthumb-5 {
    & img {
        @media #{$xl} {
            height: 178px;
            object-fit: cover;
        }
        @media #{$lg} {
            height: 185px;
            object-fit: cover;
        }
    }
}

.tpslidertwo {
    &__img {
        & img {
            @media #{$lg} {
                max-width: 100%;
            }
        }

        &-shape {
            position: absolute;
            bottom: 160px;
            inset-inline-end: -150px;
            animation: rotate 15s linear infinite;
            @media #{$lg} {
                bottom: 120px;
                inset-inline-end: 0;
            }
            @media #{$md} {
                bottom: 50px;
                inset-inline-end: 0;
            }
        }
    }

    &__content {
        & p {
            font-size: 18px;
            color: var(--tp-text-secondary);
            margin-bottom: 45px;
            @media #{$lg} {
                margin-bottom: 20px;
            }
        }
    }

    &__item {
        @media #{$xs} {
            margin-bottom: 20px;
        }
    }

    &__sub-title {
        font-size: 20px;
        color: var(--tp-text-primary);
        margin-bottom: 0;
    }

    &__title {
        font-size: 60px;
        font-weight: 600;
        @media #{$xl} {
            font-size: 48px;
        }
        @media #{$lg} {
            font-size: 48px;
        }
        @media #{$md} {
            font-size: 40px;
        }
        @media #{$xs} {
            font-size: 26px;
        }
        @media #{$sm} {
            font-size: 48px;
        }
    }
}

.tpslider-btn-4 {
    @media #{$xs} {
        margin-inline-end: 8px;
    }
}

.slider-pagination-2 {
    & .slidertwo_pagination {
        position: absolute;
        bottom: 55px;
        z-index: 9;
        inset-inline-start: 0;
        @media #{$md} {
            bottom: 15px;
        }
        @media #{$xs} {
            margin-bottom: 10px;
        }

        & .swiper-pagination-bullet {
            width: 25px;
            height: 4px;
            display: inline-block;
            border-radius: 5px;
            background: var(--tp-grey-1);
            margin: 0 5px;
        }

        & .swiper-pagination-bullet-active {
            background: var(--tp-text-primary);
        }
    }
}

.tp-slide-item__slide-btn {
    &.tp-btn {
        @media #{$xs} {
            font-size: 12px;
            padding: 10px;
        }
        @media #{$sm} {
            font-size: 14px;
            padding: 14px;
        }
    }
}

.secondary-slider {
    &__item {
        background-color: var(--tp-theme-10);
    }

    & .tpslidertwo__img-shape {
        bottom: 376px;
        inset-inline-end: -210px;
        @media #{$lg,$md} {
            bottom: 0;
            inset-inline-end: 0;
        }
    }

    & .tpslidertwo__slide-btn {
        & span {
            font-size: 14px;
            color: var(--tp-text-secondary);

            & b {
                font-size: 18px;
                color: var(--tp-text-body);
                display: block;
            }
        }
    }
}

.greenslider-pagination {
    position: absolute;
    bottom: 55px;
    z-index: 9;
    inset-inline-start: 0;
    inset-inline-end: 0;
    text-align: center;
    @media #{$lg} {
        bottom: 30px;
    }
    @media #{$md,$xs} {
        bottom: 15px;
    }

    & .swiper-pagination-bullet {
        width: 25px;
        height: 4px;
        display: inline-block;
        background: transparent;
        margin: 0 5px;
        height: 30px;
        width: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        position: relative;

        &::before {
            position: absolute;
            content: '';
            height: 8px;
            width: 8px;
            background-color: var(--tp-text-body);
            top: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            inset-inline-start: 50%;
        }
    }

    & .swiper-pagination-bullet-active {
        background: transparent;
        border: 1px solid #d7d9e0;
    }
}

.platinamborder {
    border: 15px solid #fff;

    & .tpslidertwo__item {
        @media #{$md} {
            margin-inline-start: 5px;
        }
    }
}

.platinam-slidershape {
    position: absolute;
    top: 65px;
    inset-inline-end: -80px;
    z-index: 2;
    animation: rotate 15s linear infinite;
    @media #{$xl} {
        top: 10px;
        inset-inline-end: -50px;
    }
    @media #{$lg} {
        inset-inline-end: -25px;
    }
    @media #{$md} {
        top: -30px;
        inset-inline-end: -45px;
    }
}

.tpsliderthree {
    &__img {
        @media #{$lg,$md} {
            padding-top: 0;
        }

        & img {
            width: 100%;
        }
    }

    &__pagination {
        position: absolute;
        bottom: 55px;
        z-index: 9;
        inset-inline-start: 0;
        inset-inline-end: 0;
        text-align: center;
        @media #{$md,$sm} {
            bottom: 15px;
        }

        & .swiper-pagination-bullet {
            width: 35px;
            height: 4px;
            display: inline-block;
            border-radius: 5px;
            background-color: var(--tp-common-white);
            margin: 0 5px;
            opacity: 1;
        }

        & .swiper-pagination-bullet-active {
            background: var(--tp-text-body);
        }
    }
}

.slider-bg-overlay {
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;

    &::before {
        position: absolute;
        content: '';
        bottom: 0;
        width: 100%;
        height: 100%;
        background-color: #040404;
        opacity: 0.9;
    }
}

.slider-3 {
    height: 570px;
    display: flex;
    align-items: center;
    @media #{$lg} {
        height: 490px;
    }
    @media #{$md} {
        height: 460px;
    }
    @media #{$xs} {
        height: 400px;
    }
}

.slider-content-3 {
    margin-top: -10px;
    margin-inline-start: 50px;
    @media #{$md} {
        margin-inline-start: 0;
    }
    @media #{$xs} {
        margin-inline-start: 0;
        padding-top: 0px;
    }
    @media #{$sm} {
        margin-inline-start: 0;
        padding-top: 0;
    }
}

.tpslider-item-5 {
    & .tp-slide-item__sub-title {
        margin-bottom: 15px;
    }
}

.slider-bg-four {
    display: flex;
    align-items: center;
    height: 570px;
    @media #{$md} {
        height: 500px;
        justify-content: center;
    }
    @media #{$sm} {
        padding-top: 120px;
        height: 400px;
        justify-content: center;
    }
    @media #{$xs} {
        padding-top: 120px;
        height: 400px;
        justify-content: center;
    }
}

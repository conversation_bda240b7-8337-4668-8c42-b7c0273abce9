(()=>{"use strict";
/*! js-cookie v3.0.5 | MIT */
function e(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)e[r]=t[r]}return e}var n=function n(t,r){function o(n,o,i){if("undefined"!=typeof document){"number"==typeof(i=e({},r,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),n=encodeURIComponent(n).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var c in i)i[c]&&(a+="; "+c,!0!==i[c]&&(a+="="+i[c].split(";")[0]));return document.cookie=n+"="+t.write(o,n)+a}}return Object.create({set:o,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var n=document.cookie?document.cookie.split("; "):[],r={},o=0;o<n.length;o++){var i=n[o].split("="),a=i.slice(1).join("=");try{var c=decodeURIComponent(i[0]);if(r[c]=t.read(a,c),e===c)break}catch(e){}}return e?r[e]:r}},remove:function(n,t){o(n,"",e({},t,{expires:-1}))},withAttributes:function(t){return n(this.converter,e({},this.attributes,t))},withConverter:function(t){return n(e({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});function t(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(!e)return;if("string"==typeof e)return r(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return r(e,n)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}document.addEventListener("DOMContentLoaded",(function(){var e=document.querySelector(".ae-anno-announcement-wrapper");if(e){var r=e.querySelectorAll(".ae-anno-announcement"),o=document.querySelector(".ae-anno-announcement__next-button"),i=document.querySelector(".ae-anno-announcement__previous-button"),a=document.querySelector(".ae-anno-announcement__dismiss-button"),c=JSON.parse(n.get("ae-anno-dismissed-announcements")||"[]"),u=1,s=function(){u>r.length?u=1:u<1&&(u=r.length),r.forEach((function(e){e.style.display="none"})),r[u-1].style.display="block"};s(),o&&o.addEventListener("click",(function(){u++,s()})),i&&i.addEventListener("click",(function(){s(u--)})),a&&a.addEventListener("click",(function(){var r=JSON.parse(a.getAttribute("data-announcement-ids"));c.push.apply(c,t(r)),n.set("ae-anno-dismissed-announcements",JSON.stringify(c),{expires:365}),e.parentNode.removeChild(e)}))}}))})();
<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Listeners;

use Bo<PERSON>ble\Ecommerce\Enums\InvoiceStatusEnum;
use Bo<PERSON>ble\Ecommerce\Events\OrderCompletedEvent;
use Illuminate\Contracts\Queue\ShouldQueue;

class UpdateInvoiceWhenOrderCompleted implements ShouldQueue
{
    public function handle(OrderCompletedEvent $event): void
    {
        $event->order->invoice()->update(['status' => InvoiceStatusEnum::COMPLETED]);
    }
}

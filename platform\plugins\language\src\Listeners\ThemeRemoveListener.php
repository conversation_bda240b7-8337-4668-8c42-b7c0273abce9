<?php

namespace Bo<PERSON>ble\Language\Listeners;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Botble\Language\Facades\Language;
use Bo<PERSON>ble\Setting\Models\Setting;
use Bo<PERSON>ble\Theme\Events\ThemeRemoveEvent;
use Bo<PERSON>ble\Theme\Facades\ThemeOption;
use Bo<PERSON>ble\Widget\Models\Widget;
use Exception;

class ThemeRemoveListener
{
    public function handle(ThemeRemoveEvent $event): void
    {
        try {
            $languages = Language::getActiveLanguage(['lang_code']);

            foreach ($languages as $language) {
                Widget::query()
                    ->where(['theme' => Widget::getThemeName($language->lang_code, theme: $event->theme)])
                    ->delete();

                Setting::query()
                    ->where(['key', 'LIKE', ThemeOption::getOptionKey('%', $language->lang_code)])
                    ->delete();
            }
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
        }
    }
}

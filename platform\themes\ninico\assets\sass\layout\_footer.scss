@use '../utils' as *;

.footer-content {
    & p {
        color: var(--footer-text-muted-color);
        font-size: 16px;

        & br {
            @media #{$xl,$lg,$md,$xs} {
                display: none;
            }
        }
    }
}

.footer-widget {
    &__title {
        text-decoration: underline;
        font-size: 16px;
        font-weight: 600;
        color: var(--footer-text-color);
    }

    &__links {
        & ul {
            & li {
                list-style: none;
                margin-bottom: 12px;
                @media #{$lg} {
                    margin-bottom: 8px;
                }

                &:last-child {
                    margin-bottom: 0;
                }

                & a {
                    font-size: 18px;
                    font-weight: 400;
                    color: var(--footer-text-muted-color);
                    @include transition(0.2s);
                    @media #{$xl,$lg,$md} {
                        font-size: 15px;
                    }

                    &:hover {
                        padding-inline-start: 3px;
                        color: var(--tp-text-primary);

                        & i {
                            color: var(--tp-text-primary);
                        }
                    }

                    & i {
                        color: var(--footer-text-color);
                        font-size: 18px;
                        width: 35px;
                        @include transition(0.2s);
                        @media #{$lg} {
                            font-size: 14px;
                            width: 25px;
                        }
                    }
                }
            }
        }
    }

    &__newsletter {
        & input {
            height: 50px;
            width: 370px;
            border: none;
            padding: 0 28px;
            margin-bottom: 10px;
            border-radius: 6px;
            @media #{$xxl} {
                width: 320px;
            }
            @media #{$xl} {
                width: 280px;
            }
            @media #{$lg,$xs} {
                width: 100%;
            }

            &::placeholder {
                font-size: 14px;
                font-weight: 400;
                color: #999999;
            }
        }
    }
}

.main-footer {
    border-bottom: 1px solid var(--footer-border-color);
}

.footer-col-2 {
    @media #{$lg,$md,$xs} {
        margin-inline-start: 0;
    }
}

.footer-col-4 {
    margin-inline-start: -20px;
    @media #{$lg} {
        margin-inline-start: -8px;
    }
    @media #{$md,$xs} {
        margin-inline-start: 0;
    }
}

.footer-col-5 {
    margin-inline-start: -30px;
    @media #{$md,$xs} {
        margin-inline-start: 0;
    }

    & p {
        font-size: 18px;
        color: var(--footer-text-muted-color);
        @media #{$lg} {
            font-size: 15px;
        }
    }
}

.footer-cta {
    &__icon {
        & i {
            height: 50px;
            width: 50px;
            text-align: center;
            border-radius: 50%;
            line-height: 50px;
            background-color: var(--tp-common-white);
            font-size: 18px;
            display: block;
            margin-inline-end: 15px;
            box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.04);
        }
    }

    &__contact {
        display: flex;
        align-items: center;
        @media #{$xs} {
            margin-bottom: 25px;
        }
    }

    &__text {
        & a {
            display: block;
            font-size: 18px;
            font-weight: 700;
            line-height: 1;
            color: var(--footer-text-color);
            @media #{$md} {
                font-size: 15px;
            }

            &:hover {
                color: var(--tp-text-primary);
            }
        }

        & span {
            font-size: 14px;
            font-weight: 400;
            line-height: 1;
            color: var(--tp-text-secondary);
        }
    }

    &__source {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        @media #{$xs} {
            display: inherit;
        }

        &-content {
            margin-inline-end: 20px;
            @media #{$md} {
                margin-inline-end: 15px;
            }
            @media #{$xs} {
                margin-inline-end: 0px;
                margin-bottom: 10px;
            }

            & p {
                color: var(--footer-text-muted-color);
                font-size: 14px;
                margin-bottom: 0;
                line-height: 1;
                @media #{$md} {
                    font-size: 12px;
                }
            }
        }

        &-title {
            font-weight: 700;
            font-size: 18px;
            line-height: 1;
            color: var(--footer-text-color);
            @media #{$md} {
                font-size: 16px;
            }
        }

        &-thumb {
            & a {
                margin-inline-start: 4px;

                &:first-child {
                    margin-inline-start: 0;
                }

                & img {
                    border-radius: 6px;
                }
            }
        }
    }
}

.footer-copyright {
    padding: 25px 0;

    &__content {
        & span {
            font-size: 16px;
            color: var(--tp-text-secondary);
            margin-bottom: 0;
            @media #{$xs} {
                display: block;
                margin-bottom: 12px;
            }

            & a {
                font-size: 16px;
                font-weight: 700;
                color: var(--tp-text-body);

                &:hover {
                    color: var(--tp-text-primary);
                }
            }
        }
    }

    &__brand {
        text-align: right;
        @media #{$sm,$xs} {
            text-align: center;
        }

        & img {
            @media #{$xs} {
                max-width: 100%;
            }
        }
    }
}

.footer-cta {
    @media #{$xs} {
        margin-top: 10px;
        margin-bottom: 10px;
    }
}

.footer-4-col-4 {
    @media #{$lg} {
        margin-inline-start: 0;
    }
}
